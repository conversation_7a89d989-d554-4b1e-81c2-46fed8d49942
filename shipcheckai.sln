Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = ********
MinimumVisualStudioVersion = ********
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "shipcheckai.AppHost", "src\shipcheckai.AppHost\shipcheckai.AppHost.csproj", "{8958A2F6-C31D-486D-ADB7-71ABC4EED39D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "shipcheckai.ServiceDefaults", "src\shipcheckai.ServiceDefaults\shipcheckai.ServiceDefaults.csproj", "{E5A34C76-C072-4682-AFBE-566FE36D1A1A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "shipcheckai.Api", "src\shipcheckai.Api\shipcheckai.Api.csproj", "{2EAE825C-21A3-4EA7-AE34-71039C19E786}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "shipcheckai.Entities", "src\shipcheckai.Entities\shipcheckai.Entities.csproj", "{C1D62D48-D783-4EB1-B3CE-EF3DC9094A80}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "shipcheckai.Migrations", "src\shipcheckai.Migrations\shipcheckai.Migrations.csproj", "{C42972F8-EF08-4031-B840-256A6BD53A13}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "shipcheckai.Tests", "tests\shipcheckai.Tests\shipcheckai.Tests.csproj", "{80E1DD5C-6342-4A96-9AB7-9064A1C87542}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8958A2F6-C31D-486D-ADB7-71ABC4EED39D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8958A2F6-C31D-486D-ADB7-71ABC4EED39D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8958A2F6-C31D-486D-ADB7-71ABC4EED39D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8958A2F6-C31D-486D-ADB7-71ABC4EED39D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5A34C76-C072-4682-AFBE-566FE36D1A1A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5A34C76-C072-4682-AFBE-566FE36D1A1A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5A34C76-C072-4682-AFBE-566FE36D1A1A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5A34C76-C072-4682-AFBE-566FE36D1A1A}.Release|Any CPU.Build.0 = Release|Any CPU
		{2EAE825C-21A3-4EA7-AE34-71039C19E786}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2EAE825C-21A3-4EA7-AE34-71039C19E786}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2EAE825C-21A3-4EA7-AE34-71039C19E786}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2EAE825C-21A3-4EA7-AE34-71039C19E786}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1D62D48-D783-4EB1-B3CE-EF3DC9094A80}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1D62D48-D783-4EB1-B3CE-EF3DC9094A80}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1D62D48-D783-4EB1-B3CE-EF3DC9094A80}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1D62D48-D783-4EB1-B3CE-EF3DC9094A80}.Release|Any CPU.Build.0 = Release|Any CPU
		{C42972F8-EF08-4031-B840-256A6BD53A13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C42972F8-EF08-4031-B840-256A6BD53A13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C42972F8-EF08-4031-B840-256A6BD53A13}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C42972F8-EF08-4031-B840-256A6BD53A13}.Release|Any CPU.Build.0 = Release|Any CPU
		{80E1DD5C-6342-4A96-9AB7-9064A1C87542}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{80E1DD5C-6342-4A96-9AB7-9064A1C87542}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{80E1DD5C-6342-4A96-9AB7-9064A1C87542}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{80E1DD5C-6342-4A96-9AB7-9064A1C87542}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A83D2939-ACD6-4AFF-B13E-EDE302BFF19E}
	EndGlobalSection
EndGlobal
