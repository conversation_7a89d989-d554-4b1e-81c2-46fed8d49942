.azure
######################################
# .NET
######################################
**/bin/
**/obj/
**/out/
*.user
*.suo
*.userosscache
*.sln.docstates
*.cache
*.dbmdl
*.log

# ASP.NET publish output
**/wwwroot/
!**/wwwroot/.gitkeep

# Rider / VS Code / Visual Studio
.idea/
.vscode/
*.code-workspace

######################################
# Node / React / Frontend
######################################
# Node modules
**/node_modules/

# React/Vite build output
**/dist/
**/build/

# NPM/Yarn logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Vite cache
**/.vite/

# Env files
**/.env
**/.env.*.local
**/.env.local

######################################
# OS / Misc
######################################
.DS_Store
Thumbs.db
*.swp
*.bak
*.tmp

######################################
# Docker
######################################
**/docker-compose.override.yml
**/.docker/
**/*.pid

######################################
# Aspire / Orchestration (optional)
######################################
**/AppHost/Generated/
