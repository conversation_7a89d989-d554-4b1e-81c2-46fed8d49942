<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsAspireSharedProject>true</IsAspireSharedProject>
    </PropertyGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App"/>
        <PackageReference Include="Aspire.Npgsql" Version="9.4.0" />
        <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.0" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>

        <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.7.0" />
        <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.4.0" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0"/>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Migrations\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\shipcheckai.ServiceDefaults\shipcheckai.ServiceDefaults.csproj" />
    </ItemGroup>

</Project>
