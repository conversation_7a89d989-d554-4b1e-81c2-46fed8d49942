using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using shipcheckai.Entities.Models.Ships;
using shipcheckai.Entities.Models.Users;

namespace shipcheckai.Entities.Data;

public class ShipDbContext(DbContextOptions options) : IdentityDbContext<UserData>(options)
{
    public DbSet<ShipData> Ships => Set<ShipData>();
    public DbSet<InspectionData> Inspections => Set<InspectionData>();
    public DbSet<InspectionChecklist> Checklists => Set<InspectionChecklist>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<ShipData>()
            .HasKey(s => s.Id);

        modelBuilder.Entity<ShipData>()
            .HasOne(s => s.Creator)
            .WithMany()
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<ShipData>()
            .HasMany(shipData => shipData.HaveAccess)
            .WithMany(userData => userData.Ships)
            .UsingEntity(join => join.ToTable("ShipAccess"));

        modelBuilder.Entity<InspectionData>()
            .HasKey(m => m.Id);

        modelBuilder.Entity<InspectionData>()
            .HasOne(shipMaintenanceData => shipMaintenanceData.Ship)
            .WithMany(shipData => shipData.MaintenanceLog)
            .HasForeignKey(data => data.ShipId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<InspectionData>()
            .Property(x => x.Checklist)
            .HasColumnType("jsonb");

        modelBuilder.Entity<InspectionChecklist>()
            .HasKey(m => m.Id);

        modelBuilder.Entity<InspectionChecklist>()
            .Property(x => x.Entries)
            .HasColumnType("jsonb");

        base.OnModelCreating(modelBuilder);
    }
}