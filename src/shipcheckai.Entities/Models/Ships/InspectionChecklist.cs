namespace shipcheckai.Entities.Models.Ships;

public class InspectionChecklist
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public uint Version { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsActive { get; set; } = true;
    public List<ChecklistEntryData> Entries { get; set; } = new();
}
