using shipcheckai.Entities.Models.Users;

namespace shipcheckai.Entities.Models.Ships;

public class ShipData
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string? Name { get; set; }
    public string? Description { get; set; }
    public required UserData Creator { get; set; }
    public List<UserData> HaveAccess { get; set; } = new();
    public List<InspectionData> MaintenanceLog { get; set; } = new();
}