using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace shipcheckai.Entities.Data;

// This factory is used by the EF Core tools (e.g., for creating migrations) at design time.
// It is not used by the application at runtime.
public class ShipDbContextFactory : IDesignTimeDbContextFactory<ShipDbContext>
{
    public ShipDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<ShipDbContext>();
        optionsBuilder.UseNpgsql("Host=postgres;Port=5432;Username=****;Password=****;Database=ships",
            b => b.ConfigureDataSource(x => x.EnableDynamicJson()));


        return new ShipDbContext(optionsBuilder.Options);
    }
}