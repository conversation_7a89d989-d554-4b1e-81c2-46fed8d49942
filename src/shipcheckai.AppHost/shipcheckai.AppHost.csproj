<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.3.1" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>cc0aca4e-c7db-495f-b248-86cbd0e981db</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\shipcheckai.Migrations\shipcheckai.Migrations.csproj" />
    <ProjectReference Include="..\shipcheckai.Api\shipcheckai.Api.csproj" />
    <ProjectReference Include="..\shipcheckai.Entities\shipcheckai.Entities.csproj" />
    <ProjectReference Include="..\shipcheckai.ServiceDefaults\shipcheckai.ServiceDefaults.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.NodeJs" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Aspire.Npgsql" Version="9.4.0" />
    <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.8" />
  </ItemGroup>

</Project>
