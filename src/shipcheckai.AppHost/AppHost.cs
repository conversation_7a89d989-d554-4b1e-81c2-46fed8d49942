var builder = DistributedApplication.CreateBuilder(args);


var dbUser = builder.AddParameter("DbUser", true);
var dbPassword = builder.AddParameter("DbPassword", true);

var postgres = builder.AddPostgres("postgres", dbUser, dbPassword)
    .WithPgWeb()
    .WithLifetime(ContainerLifetime.Persistent);

var database = postgres.AddDatabase("ships");

var apiService = builder.AddProject<Projects.shipcheckai_Api>("apiservice")
    .WithHttpHealthCheck("/health")
    .WithReference(database)
    .WaitFor(database);

builder.AddNpmApp("frontend", "../shipcheckai.Frontend")
    .WithExternalHttpEndpoints()
    // .WithHttpHealthCheck("/health")
    .WithReference(apiService)
    .WithEnvironment("BROWSER", "none")
    .WaitFor(apiService)
    .PublishAsDockerFile()
    .WithHttpEndpoint(5173, env: "FRONTEND_PORT");

builder.AddProject<Projects.shipcheckai_Migrations>("migration")
    .WithReference(database)
    .WaitFor(database)
    .WithParentRelationship(apiService);

builder.Build().Run();