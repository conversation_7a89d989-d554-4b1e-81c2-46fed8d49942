api-version: 2024-02-02-preview
location: {{ .Env.AZURE_LOCATION }}
identity:
  type: UserAssigned
  userAssignedIdentities:
    ? "{{ .Env.AZURE_CONTAINER_REGISTRY_MANAGED_IDENTITY_ID }}"
    : {}
properties:
  environmentId: {{ .Env.AZURE_CONTAINER_APPS_ENVIRONMENT_ID }}
  configuration:
    activeRevisionsMode: single
    runtime:
      dotnet:
        autoConfigureDataProtection: true
    ingress:
      external: false
      targetPort: 8000
      transport: http
      allowInsecure: true
    registries:
      - server: {{ .Env.AZURE_CONTAINER_REGISTRY_ENDPOINT }}
        identity: {{ .Env.AZURE_CONTAINER_REGISTRY_MANAGED_IDENTITY_ID }}
  template:
    containers:
      - image: {{ .Image }}
        name: frontend
        env:
          - name: AZURE_CLIENT_ID
            value: {{ .Env.MANAGED_IDENTITY_CLIENT_ID }}
          - name: BROWSER
            value: none
          - name: FRONTEND_PORT
            value: "8000"
          - name: NODE_ENV
            value: development
          - name: services__apiservice__http__0
            value: http://apiservice.internal.{{ .Env.AZURE_CONTAINER_APPS_ENVIRONMENT_DEFAULT_DOMAIN }}
          - name: services__apiservice__https__0
            value: https://apiservice.internal.{{ .Env.AZURE_CONTAINER_APPS_ENVIRONMENT_DEFAULT_DOMAIN }}
    scale:
      minReplicas: 1
tags:
  azd-service-name: frontend
  aspire-resource-name: frontend
