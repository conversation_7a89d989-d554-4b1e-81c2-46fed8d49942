using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Contracts.Maintenance;

namespace shipcheckai.ApiService.Endpoints;

[ApiController]
[Route("[controller]")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
public class ShipInspectionsController(
    IInspectionService inspectionService,
    ICurrentUserContext currentUser,
    ILogger<InspectionsController> logger)
    : ControllerBase
{
    [HttpGet("{shipId}")]
    public async Task<ActionResult<InspectionHistory>> GetInspectionHistory(string shipId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Getting maintenance history for ship {ShipId} for user {UserId}", shipId,
            currentUser.UserId);

        var history = await inspectionService.GetInspectionHistory(shipId);
        return Ok(history);
    }

    [HttpPost("{shipId}")]
    public async Task<ActionResult<InspectionDto>> StartInspection(string shipId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Creating maintenance for ship: {ShipId}", shipId);


        var inspectionDto = await inspectionService.StartInspectionAsync(shipId);
        return CreatedAtAction(
            nameof(InspectionsController.GetInspection),
            "Inspections",
            new {inspectionId = inspectionDto.Id},
            inspectionDto);
    }
}