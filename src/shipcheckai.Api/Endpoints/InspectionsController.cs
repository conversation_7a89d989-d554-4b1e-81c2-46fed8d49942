using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Contracts.Maintenance;

namespace shipcheckai.ApiService.Endpoints;

[ApiController]
[Route("[controller]")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
public class InspectionsController(
    IInspectionService inspectionService,
    ICurrentUserContext currentUser,
    ILogger<InspectionsController> logger)
    : ControllerBase
{
    [HttpDelete("{inspectionId}")]
    public async Task<ActionResult> DeleteInspection(string inspectionId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Deleting maintenance checkup {MaintenanceId} by user {UserId}", inspectionId,
            currentUser.UserId);

        var result = await inspectionService.DeleteInspectionAsync(inspectionId);
        return Ok(result);
    }

    [HttpGet("{inspectionId}")]
    public async Task<ActionResult<InspectionDto>> GetInspection(string inspectionId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Getting maintenance checkup {MaintenanceId} for user {UserId}", inspectionId,
            currentUser.UserId);
        var maintenance = await inspectionService.GetInspectionAsync(inspectionId);

        return Ok(maintenance);
    }

    [HttpPut("{inspectionId}")]
    public async Task<ActionResult<InspectionDto>> UpdateInspection(
        string inspectionId, [FromBody] UpdateInspectionRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        // Set the maintenance ID from the route
        request.InspectionId = inspectionId;

        logger.LogInformation("Updating inspection {InspectionId} by user {UserId}", inspectionId, currentUser.UserId);

        var result = await inspectionService.UpdateInspectionAsync(request);
        return Ok(result);
    }
}