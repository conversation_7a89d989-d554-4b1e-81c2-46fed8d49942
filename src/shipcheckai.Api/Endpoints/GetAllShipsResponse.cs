using shipcheckai.Entities.Models.Ships;

namespace shipcheckai.ApiService.Endpoints;

/// <summary>
/// </summary>
/// <param name="Ships"></param>
public class GetAllShipsResponse(List<ShipData> Ships)
{
    /// <summary>
    ///     The list of ships.
    /// </summary>
    public List<ShipData> Ships { get; init; } = Ships;

    public void Deconstruct(out List<ShipData> Ships)
    {
        Ships = this.Ships;
    }
}