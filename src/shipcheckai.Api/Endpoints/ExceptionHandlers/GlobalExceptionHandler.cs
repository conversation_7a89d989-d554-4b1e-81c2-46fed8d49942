using System.Net;
using Microsoft.AspNetCore.Diagnostics;
using shipcheckai.ApiService.Domain.Exceptions;

namespace shipcheckai.ApiService.Endpoints.Middleware;

public class GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger) : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception,
        CancellationToken cancellationToken)
    {
        logger.LogError(exception, "Exception occurred: {Message}", exception.Message);

        // Set the content type and default status code.
        httpContext.Response.ContentType = "application/json";
        var statusCode = HttpStatusCode.InternalServerError;

        var errorResponse = new
        {
            message = "An unexpected error occurred.",
            details = exception.Message
        };

        // ✨ Customize the response based on the exception type.
        switch (exception)
        {
            case EntityNotFoundException:
                statusCode = HttpStatusCode.NotFound;
                errorResponse = new {message = exception.Message, details = ""};
                break;
            case UnauthorizedAccessException:
                statusCode = HttpStatusCode.Unauthorized;
                errorResponse = new {message = exception.Message, details = ""};
                break;
        }

        httpContext.Response.StatusCode = (int) statusCode;
        await httpContext.Response.WriteAsJsonAsync(errorResponse, cancellationToken);

        // Return true to indicate that the exception has been handled.
        return true;
    }
}