using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Contracts.Users;

namespace shipcheckai.ApiService.Endpoints;

[ApiController]
[Route("users")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
public class UsersController(IUserService userService, ICurrentUserContext currentUser, ILogger<UsersController> logger)
    : ControllerBase
{
    [HttpGet]
    public async Task<ActionResult<GetUserResponse>> GetProfile()
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Getting user profile for user: {UserId}", currentUser.UserId);

        var user = await userService.GetUserByIdAsync(currentUser.UserId!);
        if (user == null)
        {
            return NotFound();
        }

        return Ok(user);
    }
}