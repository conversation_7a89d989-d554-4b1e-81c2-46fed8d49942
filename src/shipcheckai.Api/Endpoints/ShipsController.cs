using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Contracts.Ships;

namespace shipcheckai.ApiService.Endpoints;

[ApiController]
[Route("[controller]")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
public class ShipsController(IShipService shipService, ICurrentUserContext currentUser, ILogger<ShipsController> logger)
    : ControllerBase
{
    [HttpPut("{shipId}/users/{userToAdd}")]
    public async Task<ActionResult> AddUserToShip(string shipId, string userToAdd)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation(
            "Adding user {RequestUserId} to ship {ShipId} by user {UserId}", userToAdd, shipId, currentUser.UserId);

        var result = await shipService.AddUserToShipAsync(new AddUserToShipRequest
        {
            ShipId = shipId,
            UserId = userToAdd
        });
        return Ok(result);
    }

    [HttpPost]
    public async Task<ActionResult<CreateShipResponse>> CreateShip([FromBody] CreateShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Creating ship: {RequestName} for user: {UserId}", request.Name, currentUser.UserId);

        var result = await shipService.CreateShipAsync(request);
        return Ok(result);
    }

    [HttpDelete("{shipId}")]
    public async Task<ActionResult<DeleteShipResponse>> DeleteShip(string shipId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Deleting ship: {ShipId} by user: {UserId}", shipId, currentUser.UserId);

        var request = new DeleteShipRequest {ShipId = shipId};
        var result = await shipService.DeleteShipAsync(request);
        return Ok(result);
    }

    [HttpGet("{shipId}")]
    public async Task<ActionResult<GetShipResponse>> GetShip(string shipId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Getting ship: {ShipId} for user: {UserId}", shipId, currentUser.UserId);

        var request = new GetShipRequest {ShipId = shipId};
        var ship = await shipService.GetShipAsync(request);
        if (ship == null)
        {
            return NotFound();
        }

        return Ok(ship);
    }

    [HttpGet]
    public async Task<ActionResult<GetUserShipsResponse>> GetUserShips()
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation("Getting ships for user: {UserId}", currentUser.UserId);

        var ships = await shipService.GetUserShipsAsync(currentUser.UserId!);
        return Ok(ships);
    }

    [HttpDelete("{shipId}/users/{userIdToRemove}")]
    public async Task<ActionResult<RemoveUserFromShipResponse>> RemoveUserFromShip(string shipId, string userIdToRemove)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        logger.LogInformation(
            "Removing user {UserIdToRemove} from ship {ShipId} by user {RequestingUserId}", userIdToRemove, shipId,
            currentUser.UserId);

        var request = new RemoveUserFromShipRequest {ShipId = shipId, UserId = userIdToRemove};
        var result = await shipService.RemoveUserFromShipAsync(request);
        return Ok(result);
    }

    [HttpPut("{shipId}")]
    public async Task<ActionResult<UpdateShipResponse>> UpdateShip(string shipId, [FromBody] UpdateShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            return Forbid();
        }

        // Set the ship ID from the route
        request.ShipId = shipId;

        logger.LogInformation("Updating ship: {ShipId} by user: {UserId}", shipId, currentUser.UserId);

        var result = await shipService.UpdateShipAsync(request);
        return Ok(result);
    }
}