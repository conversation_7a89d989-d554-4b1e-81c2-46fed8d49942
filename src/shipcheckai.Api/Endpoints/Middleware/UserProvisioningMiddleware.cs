using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.Identity.Web;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.Entities.Models.Users;

namespace shipcheckai.ApiService.Endpoints.Middleware;

public class UserProvisioningMiddleware(RequestDelegate next)
{
    // The InvokeAsync method is the core of the middleware.
    // We now inject our new ICurrentUserContext service.
    public async Task InvokeAsync(HttpContext context, UserManager<UserData> userManager,
        ICurrentUserContext currentUserContext)
    {
        // We only care about requests that have been successfully authenticated.
        if (context.User.Identity?.IsAuthenticated ?? false)
        {
            var objectId = context.User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier");
            var issuer = context.User.FindFirstValue("iss");

            if (!string.IsNullOrEmpty(objectId) && !string.IsNullOrEmpty(issuer))
            {
                var loginInfo = new UserLoginInfo(issuer, objectId, "AzureAD");
                var user = await userManager.FindByLoginAsync(loginInfo.LoginProvider, loginInfo.ProviderKey);

                // If the user is null, it means this is their first time logging in.
                if (user == null)
                {
                    var email = context.User.FindFirstValue(ClaimTypes.Email) ??
                                context.User.FindFirstValue("preferred_username");

                    var fullName = context.User.FindFirst(ClaimConstants.Name)?.Value ?? "Unknown";

                    if (string.IsNullOrEmpty(email))
                    {
                        context.Response.StatusCode = 400;
                        await context.Response.WriteAsync("Email claim not found in token. Cannot provision user.");
                        return;
                    }

                    user = new UserData(fullName)
                    {
                        UserName = email,
                        Email = email,
                        EmailConfirmed = true
                    };

                    var createResult = await userManager.CreateAsync(user);
                    if (createResult.Succeeded)
                    {
                        await userManager.AddLoginAsync(user, loginInfo);
                    }
                    else
                    {
                        context.Response.StatusCode = 500;
                        await context.Response.WriteAsync("Failed to create user.");
                        return;
                    }
                }

                currentUserContext.SetUser(context.User, user.Id);
            }
        }

        // Pass the request to the next middleware in the pipeline.
        await next(context);
    }
}