{"openapi": "3.0.4", "info": {"title": "ShipCheckAI API", "version": "v1"}, "paths": {"/inspections/{inspectionId}": {"delete": {"tags": ["inspections"], "parameters": [{"name": "inspectionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["inspections"], "parameters": [{"name": "inspectionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}}}}}, "put": {"tags": ["inspections"], "parameters": [{"name": "inspectionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInspectionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateInspectionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateInspectionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}}}}}}, "/shipinspections/{shipId}": {"get": {"tags": ["shipinspections"], "parameters": [{"name": "shipId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InspectionHistory"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InspectionHistory"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InspectionHistory"}}}}}}, "post": {"tags": ["shipinspections"], "parameters": [{"name": "shipId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InspectionDto"}}}}}}}, "/ships/{shipId}/users/{userToAdd}": {"put": {"tags": ["ships"], "parameters": [{"name": "shipId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userToAdd", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/ships": {"post": {"tags": ["ships"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShipRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateShipRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateShipRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateShipResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateShipResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateShipResponse"}}}}}}, "get": {"tags": ["ships"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetUserShipsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetUserShipsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetUserShipsResponse"}}}}}}}, "/ships/{shipId}": {"delete": {"tags": ["ships"], "parameters": [{"name": "shipId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeleteShipResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteShipResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteShipResponse"}}}}}}, "get": {"tags": ["ships"], "parameters": [{"name": "shipId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetShipResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetShipResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetShipResponse"}}}}}}, "put": {"tags": ["ships"], "parameters": [{"name": "shipId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateShipRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateShipRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateShipRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpdateShipResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateShipResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateShipResponse"}}}}}}}, "/ships/{shipId}/users/{userIdToRemove}": {"delete": {"tags": ["ships"], "parameters": [{"name": "shipId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userIdToRemove", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RemoveUserFromShipResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RemoveUserFromShipResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RemoveUserFromShipResponse"}}}}}}}, "/users": {"get": {"tags": ["users"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetUserResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetUserResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetUserResponse"}}}}}}}}, "components": {"schemas": {"ChecklistEntryDto": {"type": "object", "properties": {"positionName": {"type": "string", "nullable": true}, "images": {"type": "array", "items": {"type": "string"}, "nullable": true}, "comments": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateShipRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "imageName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateShipResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "creatorId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeleteShipResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetShipResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "creatorId": {"type": "string", "nullable": true}, "userIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "GetUserResponse": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetUserShipsResponse": {"type": "object", "properties": {"ships": {"type": "array", "items": {"$ref": "#/components/schemas/GetShipResponse"}, "nullable": true}}, "additionalProperties": false}, "InspectionDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "shipId": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date-time"}, "version": {"type": "integer", "format": "int32"}, "checklist": {"type": "array", "items": {"$ref": "#/components/schemas/ChecklistEntryDto"}, "nullable": true}}, "additionalProperties": false}, "InspectionHistory": {"type": "object", "properties": {"maintenanceHistory": {"type": "array", "items": {"$ref": "#/components/schemas/InspectionDto"}, "nullable": true}}, "additionalProperties": false}, "RemoveUserFromShipResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateInspectionRequest": {"type": "object", "properties": {"inspectionId": {"type": "string", "nullable": true}, "entries": {"type": "array", "items": {"$ref": "#/components/schemas/ChecklistEntryDto"}, "nullable": true}}, "additionalProperties": false}, "UpdateShipRequest": {"type": "object", "properties": {"shipId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateShipResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://login.microsoftonline.com/00000000-0000-0000-0000-000000000000/oauth2/v2.0/authorize", "tokenUrl": "https://login.microsoftonline.com/00000000-0000-0000-0000-000000000000/oauth2/v2.0/token", "scopes": {"api://00000000-0000-0000-0000-000000000000/.default": "Access the API", "email": "User email", "openid": "User ID", "profile": "User profile info"}}}}}}, "security": [{"oauth2": ["api://00000000-0000-0000-0000-000000000000/.default"]}]}