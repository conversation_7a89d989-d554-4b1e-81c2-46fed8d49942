using shipcheckai.ApiService.Services.interfaces;

namespace shipcheckai.ApiService.Services;

public class CurrentUserContext : ICurrentUserContext
{
    /// <summary>
    ///     The internal database ID of the authenticated user.
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    ///     A flag indicating if the current request is from an authenticated user.
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    ///     Populates the context with user information from the validated claims principal.
    /// </summary>
    /// <param name="principal">The user's ClaimsPrincipal.</param>
    /// <param name="userId">The user's internal database ID.</param>
    public void SetUser(System.Security.Claims.ClaimsPrincipal principal, string userId)
    {
        // Only set the data if the principal is authenticated.
        if (principal.Identity is {IsAuthenticated: true})
        {
            IsAuthenticated = true;
            UserId = userId;
        }
    }
}