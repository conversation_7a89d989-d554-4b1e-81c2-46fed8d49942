using Microsoft.EntityFrameworkCore;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Entities.Data;
using shipcheckai.Entities.Models.Ships;

namespace shipcheckai.ApiService.Services;

public class ChecklistService(ShipDbContext context) : IChecklistService
{
    public async Task<InspectionChecklist?> GetChecklistAsync(Guid shipId)
    {
        // Get the latest maintenance setup (highest version)
        var setup = await context.Checklists
            .Where(s => s.IsActive)
            .OrderByDescending(s => s.Version)
            .FirstOrDefaultAsync();

        return setup;
    }
}