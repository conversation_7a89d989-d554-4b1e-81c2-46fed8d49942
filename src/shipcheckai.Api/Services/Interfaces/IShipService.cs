using shipcheckai.Contracts.Ships;

namespace shipcheckai.ApiService.Services.Interfaces;

public interface IShipService
{
    Task<AddUserToShipResponse> AddUserToShipAsync(AddUserToShipRequest request);
    Task<CreateShipResponse> CreateShipAsync(CreateShipRequest request);
    Task<DeleteShipResponse> DeleteShipAsync(DeleteShipRequest request);
    Task<GetShipResponse?> GetShipAsync(GetShipRequest request);
    Task<GetUserShipsResponse> GetUserShipsAsync(string externalId);
    Task<RemoveUserFromShipResponse> RemoveUserFromShipAsync(RemoveUserFromShipRequest request);
    Task<UpdateShipResponse> UpdateShipAsync(UpdateShipRequest request);
}