using shipcheckai.Contracts.Maintenance;

namespace shipcheckai.ApiService.Services.Interfaces;

public interface IInspectionService
{
    Task<bool> DeleteInspectionAsync(string inspectionId);

    Task<InspectionDto> GetInspectionAsync(string inspectionId);

    Task<InspectionHistory> GetInspectionHistory(string shipId);

    Task<InspectionDto> StartInspectionAsync(string shipId);

    Task<InspectionDto> UpdateInspectionAsync(UpdateInspectionRequest request);
}