namespace shipcheckai.ApiService.Services.interfaces;

public interface ICurrentUserContext
{
    /// <summary>
    ///     The internal database ID of the authenticated user.
    /// </summary>
    string? UserId { get; set; }

    /// <summary>
    ///     A flag indicating if the current request is from an authenticated user.
    /// </summary>
    bool IsAuthenticated { get; set; }

    public bool IsAuthenticatedAndValid => IsAuthenticated && !string.IsNullOrEmpty(UserId);

    /// <summary>
    ///     Populates the context with user information from the validated claims principal.
    /// </summary>
    /// <param name="principal">The user's ClaimsPrincipal.</param>
    /// <param name="userId">The user's internal database ID.</param>
    void SetUser(System.Security.Claims.ClaimsPrincipal principal, string userId);
}