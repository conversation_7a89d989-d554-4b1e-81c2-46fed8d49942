using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using shipcheckai.ApiService.Domain.Exceptions;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Contracts.Users;
using shipcheckai.Entities.Models.Users;

namespace shipcheckai.ApiService.Services;

public class UserService(UserManager<UserData> userManager, ILogger<UserService> logger)
    : IUserService
{
    public async Task<GetUserResponse?> GetUserByIdAsync(string userId)
    {
        logger.LogInformation("Getting user by ID: {UserId}", userId);

        var user = await userManager.FindByIdAsync(userId);
        if (user == null)
        {
            logger.LogWarning("User not found with ID: {UserId}", userId);
            return null;
        }

        return new GetUserResponse
        {
            Email = user.Email ?? string.Empty,
            FullName = user.FullName
        };
    }

    public async Task<GetAllUsersResponse> GetAllUsersAsync()
    {
        logger.LogInformation("Getting all users");

        var users = await userManager.Users.ToListAsync();

        return new GetAllUsersResponse
        {
            Users = users.Select(u => new GetUserResponse
            {
                Email = u.Email ?? string.Empty,
                FullName = u.FullName
            }).ToList()
        };
    }

    public async Task<UpdateUserResponse> UpdateUserAsync(UpdateUserRequest request)
    {
        logger.LogInformation("Updating user: {UserId}", request.UserId);

        var user = await userManager.FindByIdAsync(request.UserId);
        if (user == null)
        {
            logger.LogWarning("User not found for update: {UserId}", request.UserId);
            throw new EntityNotFoundException("User", request.UserId);
        }

        user.FullName = request.FullName;
        user.Email = request.Email;
        user.UserName = request.Email;

        var result = await userManager.UpdateAsync(user);
        if (!result.Succeeded)
        {
            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            logger.LogError("Failed to update user {UserId}: {Errors}", request.UserId, errors);
            throw new InvalidOperationException($"Failed to update user: {errors}");
        }

        logger.LogInformation("Successfully updated user: {UserId}", request.UserId);
        return new UpdateUserResponse {Message = "User updated successfully"};
    }
}