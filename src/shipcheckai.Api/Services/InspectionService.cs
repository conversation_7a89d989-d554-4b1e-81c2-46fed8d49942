using Microsoft.EntityFrameworkCore;
using shipcheckai.ApiService.Domain.Exceptions;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Contracts.Maintenance;
using shipcheckai.Entities.Data;
using shipcheckai.Entities.Models.Ships;

namespace shipcheckai.ApiService.Services;

public class InspectionService(
    ShipDbContext context,
    ICurrentUserContext currentUser,
    ILogger<InspectionService> logger,
    IChecklistService checklistService)
    : IInspectionService
{
    private InspectionDto GetInspectionDto(InspectionData m)
    {
        return new InspectionDto
        {
            Id = m.Id.ToString(),
            ShipId = m.ShipId.ToString(),
            Date = m.Date,
            Version = m.Version,
            Checklist = m.Checklist.Select(e => new ChecklistEntryDto
                {PositionName = e.PositionName, Images = e.Images, Comments = e.Comments}).ToList()
        };
    }

    private async Task<InspectionData> GetInspectionData(string inspectionId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        var inspectionData = await context.Inspections
            .Include(m => m.Ship)
            .ThenInclude(s => s.HaveAccess)
            .FirstOrDefaultAsync(m => m.Id == Guid.Parse(inspectionId));

        if (inspectionData == null)
        {
            logger.LogWarning("Inspection not found: {InspectionID}", inspectionId);
            throw new EntityNotFoundException("Inspection", inspectionId);
        }

        // Check if user has access to this ship
        if (inspectionData.Ship.HaveAccess.Any(u => u.Id == userId))
        {
            return inspectionData;
        }

        logger.LogWarning(
            "User {UserId} does not have access to inspection {InspectionId}", userId, inspectionId);
        throw new UnauthorizedAccessException("You do not have access to this maintenance checkup");
    }

    public async Task<InspectionDto> UpdateInspectionAsync(UpdateInspectionRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        var inspectionId = request.InspectionId;
        logger.LogInformation("Updating ship maintenance: {InspectionId} by user: {UserId}", inspectionId,
            userId);

        var inspection = await GetInspectionData(inspectionId);

        // Replace the entire entries list
        inspection.Checklist = request.Entries.Select(e => new ChecklistEntry
        {
            PositionName = e.PositionName,
            Images = e.Images.ToList(),
            Comments = e.Comments.ToList()
        }).ToList();

        await context.SaveChangesAsync();

        logger.LogInformation("Successfully updated ship maintenance: {InspectionId}", inspectionId);

        return GetInspectionDto(inspection);
    }

    public async Task<InspectionHistory> GetInspectionHistory(string shipId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation(
            "Getting maintenance history for ship: {RequestShipId} for user: {UserId}", shipId, userId);

        var ship = await context.Ships
            .Include(s => s.Creator)
            .Include(s => s.HaveAccess)
            .FirstOrDefaultAsync(s => s.Id == Guid.Parse(shipId));

        if (ship == null)
        {
            logger.LogWarning("Ship not found: {RequestShipId}", shipId);
            throw new ArgumentException("Ship not found");
        }

        // Check if user has access to this ship
        if (ship.HaveAccess.All(u => u.Id != userId))
        {
            logger.LogWarning("User {UserId} does not have access to ship {RequestShipId}", userId, shipId);
            throw new UnauthorizedAccessException("You do not have access to this ship");
        }

        var inspections = await context.Inspections
            .Where(m => m.ShipId == Guid.Parse(shipId))
            .OrderByDescending(m => m.Date)
            .ToListAsync();

        return new InspectionHistory
        {
            MaintenanceHistory = inspections.Select(GetInspectionDto).ToList()
        };
    }

    public async Task<InspectionDto> StartInspectionAsync(string shipId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation("Starting inspection for ship {RequestShipId} by user {UserId}", shipId, userId);

        var ship = await context.Ships
            .Include(s => s.HaveAccess)
            .FirstOrDefaultAsync(s => s.Id == Guid.Parse(shipId));

        if (ship == null)
        {
            logger.LogWarning("Ship not found: {RequestShipId}", shipId);
            throw new ArgumentException("Ship not found");
        }

        // Check if user has access to this ship
        if (ship.HaveAccess.All(u => u.Id != userId))
        {
            logger.LogWarning("User {UserId} does not have access to ship {RequestShipId}", userId, shipId);
            throw new UnauthorizedAccessException("You do not have access to this ship");
        }

        // Get the latest maintenance setup (highest version)
        var setup = await checklistService.GetChecklistAsync(ship.Id);

        if (setup == null)
        {
            logger.LogWarning("No active maintenance setup found for ship {ShipId}", ship.Id);
            throw new ArgumentException("No active maintenance setup found");
        }

        var maintenance = new InspectionData
        {
            Ship = ship,
            ShipId = ship.Id,
            Date = DateTime.UtcNow,
            Version = setup.Version,
            Checklist = setup.Entries.Select(e => new ChecklistEntry
            {
                PositionName = e.Item,
                Images = [],
                Comments = []
            }).ToList()
        };

        context.Inspections.Add(maintenance);
        await context.SaveChangesAsync();

        logger.LogInformation("Successfully started maintenance checkup: {MaintenanceId}", maintenance.Id);

        return new InspectionDto
        {
            Id = maintenance.Id.ToString(),
            ShipId = maintenance.ShipId.ToString(),
            Date = maintenance.Date,
            Version = maintenance.Version,
            Checklist = maintenance.Checklist.Select(e => new ChecklistEntryDto
            {
                PositionName = e.PositionName,
                Images = e.Images,
                Comments = e.Comments
            }).ToList()
        };
    }

    public async Task<bool> DeleteInspectionAsync(string inspectionId)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation(
            "Deleting maintenance checkup: {RequestMaintenanceId} by user: {UserId}", inspectionId, userId);

        var inspection = await GetInspectionData(inspectionId);

        context.Inspections.Remove(inspection);
        await context.SaveChangesAsync();

        logger.LogInformation("Successfully deleted inspection: {InspectionId}", inspectionId);
        return true;
    }

    public async Task<InspectionDto> GetInspectionAsync(string inspectionId)
    {
        var inspection = await GetInspectionData(inspectionId);

        return GetInspectionDto(inspection);
    }
}