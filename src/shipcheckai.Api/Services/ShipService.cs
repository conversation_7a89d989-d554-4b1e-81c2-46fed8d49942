using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using shipcheckai.ApiService.Domain.Exceptions;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Contracts.Ships;
using shipcheckai.Entities.Data;
using shipcheckai.Entities.Models.Ships;
using shipcheckai.Entities.Models.Users;

namespace shipcheckai.ApiService.Services;

public class ShipService(
    ShipDbContext context,
    UserManager<UserData> userManager,
    ILogger<ShipService> logger,
    ICurrentUserContext currentUser)
    : IShipService
{
    public async Task<CreateShipResponse> CreateShipAsync(CreateShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation("Creating ship: {ShipName} for creator: {CreatorId}", request.Name, userId);

        var creator = await userManager.FindByIdAsync(userId!);
        if (creator == null)
        {
            logger.LogWarning("Creator not found: {CreatorId}", userId);
            throw new EntityNotFoundException("User", userId!);
        }

        var ship = new ShipData
        {
            Name = request.Name,
            Description = request.Description,
            Creator = creator
        };

        // Add creator to HaveAccess list
        ship.HaveAccess.Add(creator);

        context.Ships.Add(ship);
        await context.SaveChangesAsync();

        logger.LogInformation("Successfully created ship: {ShipId}", ship.Id);

        return new CreateShipResponse
        {
            Id = ship.Id.ToString(),
            Name = ship.Name ?? string.Empty,
            Description = ship.Description ?? string.Empty,
            CreatorId = creator.Id
        };
    }

    public async Task<UpdateShipResponse> UpdateShipAsync(UpdateShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation("Updating ship: {ShipId} by user: {UserId}", request.ShipId, userId);

        var ship = await context.Ships
            .Include(s => s.Creator)
            .FirstOrDefaultAsync(s => s.Id == Guid.Parse(request.ShipId));

        if (ship == null)
        {
            logger.LogWarning("Ship not found: {ShipId}", request.ShipId);
            throw new EntityNotFoundException("Ship", request.ShipId);
        }

        if (ship.Creator.Id != userId)
        {
            logger.LogWarning("User {UserId} is not authorized to update ship {ShipId}", userId,
                request.ShipId);
            throw new UnauthorizedAccessException("Only the ship creator can update the ship");
        }

        ship.Name = request.Name;
        ship.Description = request.Description;

        await context.SaveChangesAsync();

        logger.LogInformation("Successfully updated ship: {ShipId}", request.ShipId);
        return new UpdateShipResponse {Message = "Ship updated successfully"};
    }

    public async Task<DeleteShipResponse> DeleteShipAsync(DeleteShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation("Deleting ship: {RequestShipId} by user: {UserId}", request.ShipId, userId);

        var ship = await context.Ships
            .Include(s => s.Creator)
            .FirstOrDefaultAsync(s => s.Id == Guid.Parse(request.ShipId));

        if (ship == null)
        {
            logger.LogWarning("Ship not found: {RequestShipId}", request.ShipId);
            throw new EntityNotFoundException("Ship", request.ShipId);
        }

        if (ship.Creator.Id != userId)
        {
            logger.LogWarning(
                "User {UserId} is not authorized to delete ship {RequestShipId}", userId, request.ShipId);
            throw new UnauthorizedAccessException("Only the ship creator can delete the ship");
        }

        context.Ships.Remove(ship);
        await context.SaveChangesAsync();

        logger.LogInformation("Successfully deleted ship: {RequestShipId}", request.ShipId);
        return new DeleteShipResponse {Message = "Ship deleted successfully"};
    }

    public async Task<GetShipResponse?> GetShipAsync(GetShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation("Getting ship: {RequestShipId} for user: {UserId}", request.ShipId, userId);

        var ship = await context.Ships
            .Include(s => s.Creator)
            .Include(s => s.HaveAccess)
            .FirstOrDefaultAsync(s => s.Id == Guid.Parse(request.ShipId));

        if (ship == null)
        {
            logger.LogWarning("Ship not found: {RequestShipId}", request.ShipId);
            throw new EntityNotFoundException("Ship", request.ShipId);
        }

        // Check if user has access to this ship
        if (ship.Creator.Id != userId && ship.HaveAccess.All(u => u.Id != userId))
        {
            logger.LogWarning("User {UserId} does not have access to ship {RequestShipId}", userId, request.ShipId);
            throw new UnauthorizedAccessException("You do not have access to this ship");
        }

        return new GetShipResponse
        {
            Id = ship.Id.ToString(),
            Name = ship.Name ?? string.Empty,
            Description = ship.Description ?? string.Empty,
            CreatorId = ship.Creator.Id,
            UserIds = ship.HaveAccess.Select(u => u.Id).ToList()
        };
    }

    public async Task<GetUserShipsResponse> GetUserShipsAsync(string externalId)
    {
        logger.LogInformation("Getting ships for user: {UserId}", externalId);

        var ships = await context.Ships
            .Include(s => s.Creator)
            .Include(s => s.HaveAccess)
            .Where(s => s.Creator.Id == externalId || s.HaveAccess.Any(u => u.Id == externalId))
            .ToListAsync();

        return new GetUserShipsResponse
        {
            Ships = ships.Select(s => new GetShipResponse
            {
                Id = s.Id.ToString(),
                Name = s.Name ?? string.Empty,
                Description = s.Description ?? string.Empty,
                CreatorId = s.Creator.Id,
                UserIds = s.HaveAccess.Select(u => u.Id).ToList()
            }).ToList()
        };
    }

    public async Task<AddUserToShipResponse> AddUserToShipAsync(AddUserToShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        var shipId = request.ShipId;
        logger.LogInformation(
            "Adding user {RequestUserId} to ship {RequestShipId} by user {RequestingUserId}", request.UserId,
            shipId, userId);

        var ship = await context.Ships
            .Include(s => s.Creator)
            .Include(s => s.HaveAccess)
            .FirstOrDefaultAsync(s => s.Id == Guid.Parse(shipId));

        if (ship == null)
        {
            logger.LogWarning("Ship not found: {RequestShipId}", shipId);
            throw new EntityNotFoundException("Ship", shipId);
        }

        if (ship.Creator.Id != userId)
        {
            logger.LogWarning(
                "User {RequestingUserId} is not authorized to add users to ship {RequestShipId}", userId,
                shipId);
            throw new UnauthorizedAccessException("Only the ship creator can add users to the ship");
        }

        var userToAdd = await userManager.FindByIdAsync(request.UserId);
        if (userToAdd == null)
        {
            logger.LogWarning("User to add not found: {RequestUserId}", request.UserId);
            throw new EntityNotFoundException("User", request.UserId);
        }

        if (ship.HaveAccess.Any(u => u.Id == request.UserId))
        {
            logger.LogWarning(
                "User {RequestUserId} already has access to ship {RequestShipId}", request.UserId, shipId);
            return new AddUserToShipResponse {Message = "User already has access to this ship"};
        }

        ship.HaveAccess.Add(userToAdd);
        await context.SaveChangesAsync();

        logger.LogInformation(
            "Successfully added user {RequestUserId} to ship {RequestShipId}", request.UserId, shipId);
        return new AddUserToShipResponse {Message = "User added to ship successfully"};
    }

    public async Task<RemoveUserFromShipResponse> RemoveUserFromShipAsync(RemoveUserFromShipRequest request)
    {
        if (!currentUser.IsAuthenticatedAndValid)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }

        var userId = currentUser.UserId;

        logger.LogInformation(
            "Removing user {RequestUserId} from ship {RequestShipId} by user {RequestingUserId}", request.UserId,
            request.ShipId, userId);

        var ship = await context.Ships
            .Include(s => s.Creator)
            .Include(s => s.HaveAccess)
            .FirstOrDefaultAsync(s => s.Id == Guid.Parse(request.ShipId));

        if (ship == null)
        {
            logger.LogWarning("Ship not found: {RequestShipId}", request.ShipId);
            throw new EntityNotFoundException("Ship", request.ShipId);
        }

        if (ship.Creator.Id != userId)
        {
            logger.LogWarning(
                "User {RequestingUserId} is not authorized to remove users from ship {RequestShipId}", userId,
                request.ShipId);
            throw new UnauthorizedAccessException("Only the ship creator can remove users from the ship");
        }

        var userToRemove = ship.HaveAccess.FirstOrDefault(u => u.Id == request.UserId);
        if (userToRemove == null)
        {
            logger.LogWarning(
                "User {RequestUserId} does not have access to ship {RequestShipId}", request.UserId, request.ShipId);
            throw new EntityNotFoundException("User", request.UserId);
        }

        ship.HaveAccess.Remove(userToRemove);
        await context.SaveChangesAsync();

        logger.LogInformation(
            "Successfully removed user {RequestUserId} from ship {RequestShipId}", request.UserId, request.ShipId);
        return new RemoveUserFromShipResponse {Message = "User removed from ship successfully"};
    }
}