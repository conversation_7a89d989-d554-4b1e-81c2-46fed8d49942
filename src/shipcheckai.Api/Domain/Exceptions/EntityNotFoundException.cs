namespace shipcheckai.ApiService.Domain.Exceptions;

public class EntityNotFoundException : Exception
{
    public EntityNotFoundException()
    {
    }

    public EntityNotFoundException(string message) : base(message)
    {
    }

    public EntityNotFoundException(string message, Exception inner) : base(message, inner)
    {
    }

    // ✨ Custom constructor for more context
    public EntityNotFoundException(string entityName, object id)
        : base($"Entity '{entityName}' with ID '{id}' was not found.")
    {
    }
}