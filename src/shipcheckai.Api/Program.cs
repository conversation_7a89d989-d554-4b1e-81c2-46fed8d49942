using System.Text.Json;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web;
using Microsoft.OpenApi.Models;
using shipcheckai.ApiService.Endpoints.Middleware;
using shipcheckai.ApiService.Services;
using shipcheckai.ApiService.Services.interfaces;
using shipcheckai.ApiService.Services.Interfaces;
using shipcheckai.Entities.Data;
using shipcheckai.Entities.Models.Users;

var builder = WebApplication.CreateBuilder(args);

// Add service defaults & Aspire client integrations.
builder.AddServiceDefaults();

builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
// Add services to the container.
builder.Services.AddProblemDetails();
builder.Services.AddCors();

// Add Controllers
builder.Services.AddControllers();
builder.Services.AddRouting(options =>
{
    options.LowercaseUrls = true;
    options.LowercaseQueryStrings = true; // optional
});

builder.Services.AddEndpointsApiExplorer();

var tenantId = builder.Configuration["AzureAd:TenantId"] ?? "********-0000-0000-0000-************";
var clientId = builder.Configuration["AzureAd:ClientId"] ?? "********-0000-0000-0000-************";

var swaggerClientId = builder.Configuration["AzureAd:Swagger:ClientId"];
var apiScopes = builder.Configuration["AzureAd:Swagger:Scope"] ?? "api://********-0000-0000-0000-************/.default";

// Add OpenAPI/Swagger1

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "ShipCheckAI API",
        Version = "v1"
    });
    c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.OAuth2,
        Flows = new OpenApiOAuthFlows
        {
            AuthorizationCode = new OpenApiOAuthFlow
            {
                AuthorizationUrl = new Uri($"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/authorize"),
                TokenUrl = new Uri($"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token"),
                Scopes = new Dictionary<string, string>
                {
                    {apiScopes, "Access the API"},
                    {"email", "User email"},
                    {"openid", "User ID"},
                    {"profile", "User profile info"}
                }
            }
        }
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference {Type = ReferenceType.SecurityScheme, Id = "oauth2"}
            },
            [apiScopes]
        }
    });


    c.TagActionsBy(api =>
    {
        var tag = api.GroupName ?? api.ActionDescriptor.RouteValues["controller"];
        return [tag?.ToLowerInvariant() ?? string.Empty];
    });
});

// Configure OpenAPI generation
builder.Services.Configure<Microsoft.AspNetCore.Http.Json.JsonOptions>(options =>
{
    options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
});


builder.Services.AddIdentity<UserData, IdentityRole>(options => options.SignIn.RequireConfirmedAccount = false)
    .AddEntityFrameworkStores<ShipDbContext>()
    .AddDefaultTokenProviders(); // For password reset, email confirmation tokens etc.


builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"));

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("access_as_user", policy =>
        policy.RequireScope("access_as_user"));
});

// Register services
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IShipService, ShipService>();
builder.Services.AddScoped<IInspectionService, InspectionService>();
builder.Services.AddScoped<IChecklistService, ChecklistService>();
builder.Services.AddScoped<ICurrentUserContext, CurrentUserContext>();


builder.Services.AddDbContext<ShipDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("ships"),
        optionsBuilder => { optionsBuilder.ConfigureDataSource(x => x.EnableDynamicJson()); }));

var app = builder.Build();

app.UseAuthentication(); // IMPORTANT: Must be called before UseAuthorization
app.UseAuthorization();
app.UseMiddleware<UserProvisioningMiddleware>();
app.UseHttpsRedirection();

app.UseCors(x => x.AllowAnyOrigin());

// Configure Swagger
app.UseSwagger(c => { c.RouteTemplate = "swagger/{documentName}/swagger.json"; });

// if (app.Environment.IsDevelopment())
{
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "ShipCheckAI API v1");

        c.OAuthClientId(swaggerClientId);

        c.OAuthScopes(apiScopes, "openid", "profile", "email");
        c.OAuthUsePkce();
    });
}


// Map Controllers
app.MapControllers();


// Configure the HTTP request pipeline.
app.UseExceptionHandler();

app.MapDefaultEndpoints();

app.Run();