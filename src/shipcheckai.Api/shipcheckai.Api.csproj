<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>shipcheckai.ApiService</RootNamespace>
  </PropertyGroup>
  <PropertyGroup>
      <OpenApiGenerateDocuments>true</OpenApiGenerateDocuments>
      <OpenApiDocumentsDirectory>.</OpenApiDocumentsDirectory>
  </PropertyGroup>
  <PropertyGroup>
      <!-- Path where you want the OpenAPI JSON -->
      <OpenApiOutputFile>$(MSBuildProjectDirectory)\openapi.json</OpenApiOutputFile>
      <!-- Optional: Which document name from Swashbuckle -->
      <OpenApiDocumentName>v1</OpenApiDocumentName>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\shipcheckai.Entities\shipcheckai.Entities.csproj" />
    <ProjectReference Include="..\shipcheckai.ServiceDefaults\shipcheckai.ServiceDefaults.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspire.Hosting" Version="9.4.0" />
    <PackageReference Include="Aspire.Npgsql" Version="9.4.0" />
    <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.ApiDescription.Server" Version="9.0.8">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="3.12.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="9.0.3" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Contracts\Checklists\" />
  </ItemGroup>
  <Target Name="GenerateOpenApiSpec" AfterTargets="Build">
      <Exec Command="dotnet swagger tofile --output &quot;$(OpenApiOutputFile)&quot; $(TargetPath) $(OpenApiDocumentName)" />
  </Target>


</Project>
