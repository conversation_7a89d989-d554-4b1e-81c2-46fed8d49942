# ShipCheck.AI - Product Plan

## 1. Product Vision

To provide a seamless and efficient way for ship inspectors and owners to conduct and manage ship inspections, ensuring safety and compliance with industry standards. ShipCheck.AI simplifies the inspection process by allowing users to create digital checklists, capture photo evidence, and track the maintenance history of their vessels.

## 2. Target Audience

*   **Ship Surveyors/Inspectors:** Professionals who conduct detailed inspections of ships.
*   **Ship Owners/Managers:** Individuals or companies responsible for the maintenance and safety of one or more vessels.
*   **Crew Members:** On-board personnel who might be involved in routine checks and reporting.

## 3. User Roles and Permissions

*   **Owner:**
    *   The user who creates a ship automatically becomes its owner.
    *   Can view, edit, and delete the ship.
    *   Can add/remove other users (Inspectors) to the ship.
    *   Can view all inspection reports for the ship.
*   **Inspector:**
    *   A user who has been added to a ship by its Owner.
    *   Can view the ship and its inspection history.
    *   Can start and submit new inspections.
    *   Cannot edit or delete the ship's primary details.

## 4. Key Features

*   **User Authentication:** Secure login and registration using Microsoft Authentication Library (MSAL).
*   **Ship Management:**
    *   Create, view, update, and delete ship profiles (Owners only).
    *   Assign users to ships (Owners only).
*   **Inspection Management:**
    *   Start a new inspection for a specific ship based on a predefined checklist.
    *   Capture photos and add descriptions for each checklist item.
    *   Save an inspection as a draft (in-progress) and resume it later.
    *   Submit completed inspection reports.
    *   View a detailed history of all past inspections for a ship, including full reports with photos and notes.
*   **Checklist Management (Future):**
    *   Create and manage custom checklist templates.

## 5. User Stories

### As a Ship Owner, I want to:

*   Securely log in with my Microsoft account.
*   Automatically become the owner of any ship I create so that I have full control.
*   Be the only one who can edit or delete my ships so that the information is secure.
*   Add other registered users to my ships so that they can perform inspections.
*   Remove users from my ships so that I can manage access as my team changes.
*   See a list of all the ships in my fleet so that I can get a quick overview.
*   Review the full, detailed inspection reports for my ships so that I am aware of their maintenance status and history.

### As an Inspector, I want to:

*   Securely log in with my Microsoft account.
*   Only see ships I have been granted access to so that my dashboard is focused.
*   Start a new inspection using a predefined checklist so that all checks are consistent.
*   Save my inspection progress at any time so that I don't lose my work if I get interrupted.
*   See a list of my in-progress inspections so that I can easily resume them.
*   Take a picture and add notes for each checklist item so that I can accurately document the ship's condition.
*   Submit a completed inspection so that the report is saved and accessible.
*   View a ship's detailed inspection history so that I can track its condition over time and prepare for new inspections.

## 6. Application Pages

This section outlines the different pages in the web application and their respective components and functionality.

### 6.1. Landing Page
*   **Content:**
    *   Application name and logo.
    *   A brief, compelling description of ShipCheck.AI's purpose and benefits.
    *   A single "Login / Register" button.
*   **Functionality:**
    *   The "Login / Register" button initiates the Microsoft Authentication (MSAL) flow.

### 6.2. Dashboard Page
*   **Content:**
    *   A "Welcome, [User Name]" message.
    *   A section titled "My In-Progress Inspections" listing inspections the user has started but not completed.
    *   A section titled "My Ships" displaying a list of all ships the user has access to (either as an owner or inspector).
    *   A prominent "Create New Ship" button.
*   **Functionality:**
    *   Clicking on an in-progress inspection resumes it, navigating to the **Inspection Page**.
    *   Clicking on a ship navigates to the **Ship Details Page**.
    *   The "Create New Ship" button navigates to the **Ship Editor Page**.

### 6.3. Ship Details Page
*   **Content:**
    *   The ship's name, photo, IMO number, and other relevant details.
    *   A clear indicator if the user is the "Owner" of the ship.
*   **Functionality:**
    *   **Edit Ship** button (visible to Owner only): Navigates to the **Ship Editor Page** with the form pre-filled.
    *   **Delete Ship** button (visible to Owner only): Shows a confirmation modal before deleting the ship.
    *   **Manage Users** button (visible to Owner only): Opens a dialog where the owner can see a list of users with access and can add or remove them.
    *   **Start New Inspection** button: Navigates to the **Inspection Page** to begin a new inspection for the current ship.
    *   **View Inspection History** button: Navigates to the **Inspection History Page**.

### 6.4. Ship Editor Page (for Create/Edit)
*   **Content:**
    *   A form with fields for the ship's name, IMO number, etc.
    *   A photo upload component.
*   **Functionality:**
    *   When creating, submitting the form creates the new ship and navigates to the new **Ship Details Page**.
    *   When editing, submitting the form updates the ship's details and returns to the **Ship Details Page**.

### 6.5. Inspection Page
*   **Content:**
    *   The name of the ship being inspected.
    *   A list of the predefined checklist items. Each item will have:
        *   A placeholder for a photo.
        *   A text area for a description/notes.
        *   A "Take Photo" button.
*   **Functionality:**
    *   "Take Photo" button opens the device camera or a file browser to upload an image. The uploaded image is then displayed.
    *   "Save and Exit" button saves the current state of all entries (photos and text) and returns the user to the **Dashboard**.
    *   "Submit Inspection" button becomes active only when all checklist items have a photo. Clicking it saves the inspection as "completed" and navigates the user to the **Ship Details Page**.

### 6.6. Inspection History Page
*   **Content:**
    *   A chronological list of all completed inspections for the ship.
    *   Each item in the list shows the inspection date and the name of the inspector.
*   **Functionality:**
    *   Clicking on any inspection in the list navigates to the **Inspection Report Page**.

### 6.7. Inspection Report Page
*   **Content:**
    *   A read-only view of a completed inspection.
    *   Displays all checklist items with the photos and descriptions that were captured.
*   **Functionality:**
    *   No interactive functionality on this page besides navigation back to the history or dashboard.

## 7. API Endpoint Mapping

This section maps the functionality described in the Application Pages section to the specific generated API client functions.

### 7.1. Dashboard Page
*   **My Ships List:** `useGetShips()` - Fetches the list of ships for the current user.
*   **My In-Progress Inspections:** This will require filtering the results from `useGetShips()` or a dedicated endpoint in the future. For now, we can assume inspections are part of the ship data. A `useGetInspectionsInspectionId()` call will be made when an in-progress inspection is clicked.
*   **Create New Ship:** `usePostShips()` - To create a new ship.

### 7.2. Ship Details Page
*   **View Ship Details:** `useGetShipsShipId()` - To fetch the details of the selected ship.
*   **Edit Ship:** `usePutShipsShipId()` - To update the ship's details.
*   **Delete Ship:** `useDeleteShipsShipId()` - To delete the ship.
*   **Manage Users (Add):** `usePutShipsShipIdUsersUserToAdd()` - To add a user to the ship. A `useGetUsers()` call will be used to get a list of users to choose from.
*   **Manage Users (Remove):** `useDeleteShipsShipIdUsersUserIdToRemove()` - To remove a user from the ship.
*   **Start New Inspection:** `usePostShipinspectionsShipId()` - To create a new inspection record for the ship.
*   **View Inspection History:** `useGetShipinspectionsShipId()` - To fetch the inspection history for the ship.

### 7.3. Ship Editor Page
*   **Create Ship:** `usePostShips()` - The `CreateShipRequest` model will be used for the form data.
*   **Update Ship:** `usePutShipsShipId()` - The `UpdateShipRequest` model will be used for the form data.

### 7.4. Inspection Page
*   **Load Inspection:** `useGetInspectionsInspectionId()` - To load the details of an in-progress inspection.
*   **Save and Exit / Submit Inspection:** `usePutInspectionsInspectionId()` - This single endpoint will be used for both saving a draft and submitting a completed inspection. The `UpdateInspectionRequest` body will be updated with the latest photos and descriptions. To differentiate between save and submit, the request body might include a status field (e.g., 'InProgress' vs. 'Completed').
*   **Discard Draft:** `useDeleteInspectionsInspectionId()` - To allow users to delete an in-progress inspection.

### 7.5. Inspection History Page
*   **List History:** `useGetShipinspectionsShipId()` - The `InspectionHistory` model will contain the list of inspections to display.

### 7.6. Inspection Report Page
*   **View Report:** `useGetInspectionsInspectionId()` - To fetch the full details of a single completed inspection (`InspectionDto`).