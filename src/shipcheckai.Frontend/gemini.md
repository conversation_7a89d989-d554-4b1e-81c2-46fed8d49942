# Gemini Frontend Project Guide

This document provides a comprehensive overview of the `shipcheckai.Frontend` project. It outlines the mandatory technologies, architectural principles, and development conventions that all contributors MUST follow.

## 1. Core Development Principles

This section contains the fundamental rules for developing this project. Adherence to these principles is not optional.

### Technology Stack Mandates
- **UI Library:** You MUST use **Material-UI (MUI)** with the Emotion styling engine for all UI components. You are explicitly FORBIDDEN from using Tailwind CSS, styled-components, or shadcn/ui.
- **Routing:** You MUST use `react-router-dom` version 6+.
- **Client State:** You MUST use `zustand` for global client-side state.
- **Server State:** You MUST use the pre-existing Orval-generated React Query hooks for all server state management.

### State Management Separation of Concerns
- **React Query (via Orval)** is the SOLE authority for server state (fetching, caching, and mutating API data).
- **Zustand** is to be used ONLY for client-side UI state (e.g., theme mode, sidebar visibility, modal open/close state).
- You MUST NOT store server data, API loading states, or API error states in a Zustand store.

### TypeScript Best Practices
- **Exports:** Use named exports instead of default exports.
- **Type Definitions:** Prefer the `type` keyword over `interface` for consistency.
- **Props:** All component props MUST be explicitly typed.

---

## 2. Project Overview

This project is a modern frontend application built with React and TypeScript. It serves as the user interface for the ShipCheckAI application, interacting with a .NET backend. It is designed to be a single-page application (SPA) and is built using Vite for a fast development experience. Try to reuse components where it makes sense to reuse them and keep the architecture clean as a highly experienced frontend developer would.

## 3. Core Technologies

The frontend stack is composed of the following technologies, in alignment with the principles outlined above.

*   **Framework:** [React](https://react.dev/) 19
*   **Language:** [TypeScript](https://www.typescriptlang.org/) 5.8
*   **Build Tool:** [Vite](https://vitejs.dev/) 7.0
*   **UI Component Library:** [Material-UI (MUI)](https://mui.com/) 7.3
*   **Routing:** [React Router](https://reactrouter.com/) 7.7
*   **Data Fetching & Server State:** [TanStack Query](https://tanstack.com/query/latest) 5.81
*   **Client State Management:** [Zustand](https://zustand-demo.pmnd.rs/) 5.0
*   **API Client:** [Orval](https://orval.dev/) 7.10
*   **Authentication:** [MSAL React](https://github.com/AzureAD/microsoft-authentication-library-for-js/tree/dev/lib/msal-react)
*   **Linting:** [ESLint](https://eslint.org/) 9.29

## 4. Project Structure

The `src` directory is organized as follows:

```
src/
├── api/            # Orval-generated API client code
├── assets/         # Static assets like images and fonts
├── axios/          # Axios instance configuration for API calls
├── components/     # Reusable UI components (e.g., Buttons, Inputs)
├── features/       # Components and logic for specific application features
├── lib/            # Utility functions and helper modules
├── pages/          # Top-level page components that correspond to routes
├── providers/      # React context providers (e.g., Theme, Auth)
├── routes/         # Route definitions and layout components
├── store/          # Zustand store definitions for global state
├── App.tsx         # Main application component
├── index.css       # Global CSS styles
├── main.tsx        # Application entry point
└── vite-env.d.ts   # TypeScript definitions for Vite environment variables
```

## 5. Getting Started

### Prerequisites

*   [Node.js](https://nodejs.org/) (version 20.x or higher recommended)
*   [npm](https://www.npmjs.com/) (comes with Node.js)

### Installation

1.  Navigate to the frontend project directory: `cd src/shipcheckai.Frontend`
2.  Install dependencies: `npm install`

### Running the Development Server

```bash
npm run dev
```
The application will be available at `http://localhost:5173`.

## 6. Available Scripts

*   `npm run dev`: Starts the development server.
*   `npm run start`: An alias for `dev`.
*   `npm run build`: Bundles the app for production.
*   `npm run lint`: Lints the codebase.
*   `npm run preview`: Previews the production build.
*   `npm run orval`: Generates the API client from the backend's OpenAPI spec.

## 7. API Interaction & Server State

As mandated by the core principles, all interaction with the backend API is managed by **React Query** through a generated **Orval client**.

- **Code Generation:** Run `npm run orval` to generate fully-typed React Query hooks for every API endpoint. These are located in `src/api`.
- **Usage:** Import and use the generated hooks (e.g., `useGetShips`, `useCreateShip`) directly in your components to handle all data fetching, caching, and mutation. Do not use `axios` or `fetch` directly.

## 8. Client State Management

For client-side state, this project uses **Zustand**.

- **Purpose:** Zustand is strictly for UI state that is not persisted on the server (e.g., theme, modal visibility).
- **Location:** Stores are defined in the `src/store` directory.
- **Rule:** Do NOT store API data, loading states, or errors in Zustand. Use React Query for that.

## 9. Styling

Styling is handled exclusively by **Material-UI (MUI)** and its underlying **Emotion** engine.

*   **Components:** Use the rich set of pre-built components from MUI.
*   **Customization:** Use the `sx` prop for one-off styles or the `@emotion/styled` package for creating reusable, styled components.

## 10. Routing

Routing is managed by **`react-router-dom` v6+**.

*   **Configuration:** Routes are defined in `src/routes`.
*   **Usage:** Use the `useNavigate` hook and the `Link` component for navigation. Leverage nested routes and layouts for shared UI.