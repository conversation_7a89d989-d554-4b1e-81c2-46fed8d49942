import {defineConfig, loadEnv} from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
    
  return {
    plugins: [react(), tsconfigPaths()],
    server:{
      port: parseInt(env.FRONTEND_PORT),
      proxy: {
        '/api': {
          target: process.env.services__apiservice__https__0 ||
            process.env.services__apiservice__http__0,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          secure: false,
        }
      }
    }
  }
})
