import { AppB<PERSON>, Box, Container, IconButton, Toolbar, Typography } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { Outlet } from 'react-router-dom';
import { useAppStore } from '@/store/useAppStore';

export function AppLayout() {
  const toggle = useAppStore((s) => s.toggleMobileNav);
  return (
    <Box sx={{ display: 'flex', minHeight: '100dvh' }}>
      <AppBar position="static">
        <Toolbar>
          <IconButton color="inherit" edge="start" onClick={toggle} sx={{ mr: 2 }}>
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            ShipCheckAI
          </Typography>
        </Toolbar>
      </AppBar>
      <Container sx={{ py: 3, flex: 1 }}>
        <Outlet />
      </Container>
    </Box>
  );
}

