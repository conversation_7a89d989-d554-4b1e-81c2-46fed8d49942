// src/lib/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // With this setting, queries will not refetch on mount if the data
      // is less than 5 minutes old. This improves UX by avoiding
      // unnecessary loading spinners for recently viewed data.
      staleTime: 1000 * 60 * 5, // 5 minutes

      // Inactive queries will be removed from the cache after 30 minutes.
      // This is a garbage collection setting.
      gcTime: 1000 * 60 * 30, // 30 minutes in v5, formerly cacheTime in v4

      // Optional: A sensible default to prevent refetches just because
      // the user clicks away and back to the browser window.
      refetchOnWindowFocus: false,
    },
  },
});