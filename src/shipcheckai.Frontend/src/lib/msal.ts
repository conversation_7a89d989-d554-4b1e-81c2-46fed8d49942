import { type Configuration, PublicClientApplication } from '@azure/msal-browser';

export const msalConfig: Configuration = {
  auth: {
    clientId: '020bfc2a-1415-42a0-a763-bdece6eae4a5',
    authority: 'https://shipcheckai.ciamlogin.com/27638497-6106-47f5-a48d-0be490d9d194',
    redirectUri: 'http://localhost:5173/users',
    knownAuthorities: ["shipcheckai.ciamlogin.com"]
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (_level, message, containsPii) => {
        if (!containsPii) {
          console.log("MSAL: " + message);
        }
      },
      logLevel: 3,
    },
  },
};

export const loginRequest = {
  scopes: ['api://6dbe6250-b5fc-4ccb-a1b0-17d7de63d76a/access_as_user'],
};

export const msalInstance = new PublicClientApplication(msalConfig);

