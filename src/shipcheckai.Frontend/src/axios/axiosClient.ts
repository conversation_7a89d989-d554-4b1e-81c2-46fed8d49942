import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  type InternalAxiosRequestConfig,
} from 'axios';
import { msalInstance, loginRequest } from '@/lib/msal';
import { InteractionRequiredAuthError, type AuthenticationResult } from '@azure/msal-browser';

// A singleton promise to prevent concurrent token requests
let tokenAcquisitionPromise: Promise<AuthenticationResult> | null = null;

// ✅ **REVISED FUNCTION**
const acquireToken = (): Promise<AuthenticationResult> => {
  if (tokenAcquisitionPromise) {
    return tokenAcquisitionPromise;
  }

  tokenAcquisitionPromise = new Promise(async (resolve, reject) => {
    // Get all accounts from the cache.
    const accounts = msalInstance.getAllAccounts();

    // **1. Handle the "No User Logged In" case**
    if (accounts.length === 0) {
      try {
        // No user is signed in. Trigger a popup to log them in.
        console.log("No account found. Initiating login popup.");
        const response = await msalInstance.loginPopup(loginRequest);
        // Set the newly logged-in account as active
        msalInstance.setActiveAccount(response.account);
        resolve(response);
      } catch (error) {
        // User closed the popup or login failed
        reject(error);
      } finally {
        tokenAcquisitionPromise = null;
      }
      return;
    }

    // **2. Handle the "User is Logged In" case**
    // If we have an account, try to get a token silently.
    try {
      const silentRequest = {
        ...loginRequest,
        account: accounts[0], // Use the first available account
      };
      const response = await msalInstance.acquireTokenSilent(silentRequest);
      resolve(response);
    } catch (error) {
      // If silent acquisition fails, fall back to a popup
      if (error instanceof InteractionRequiredAuthError) {
        try {
          const response = await msalInstance.acquireTokenPopup(loginRequest);
          resolve(response);
        } catch (popupError) {
          reject(popupError);
        }
      } else {
        reject(error);
      }
    } finally {
      tokenAcquisitionPromise = null;
    }
  });

  return tokenAcquisitionPromise;
};


const axiosInstance: AxiosInstance = axios.create({
  baseURL: '/api',
});

axiosInstance.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
  if (config.url?.includes('login.microsoftonline.com')) {
    return config;
  }

  try {
    const response = await acquireToken();
    if (response.accessToken) {
      config.headers.Authorization = `Bearer ${response.accessToken}`;
    }
  } catch (error) {
    console.error("Token acquisition failed:", error);
    return Promise.reject(error);
  }

  return config;
}, (error) => Promise.reject(error));


export const customInstance = <T>(
  options: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return axiosInstance.request<T>(options);
};