import { <PERSON><PERSON>, Card, CardContent, List, ListItem, Skeleton, Typography } from '@mui/material';
import { useGetUsers } from '@/api/endpoints/users';
import type {GetUserResponse} from "@/api/model";

export function UsersListPage() {
  const { data, isLoading, isError } = useGetUsers();

  if (isLoading) {
    return (
      <>
        {[1, 2, 3].map((k) => (
          <Skeleton key={k} height={56} />
        ))}
      </>
    );
  }

  if (isError) {
    return <Alert severity="error">Failed to fetch users.</Alert>;
  }

  const userData = (data?.data as GetUserResponse);
  
  
  const users = userData == null ? [] : [userData];

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Users
        </Typography>
        <List>
          {users.map((u: any, idx: number) => (
            <ListItem key={idx}>{u.fullName ?? u.email ?? 'Unknown'}</ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
}

