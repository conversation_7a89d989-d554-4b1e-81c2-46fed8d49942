/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * ShipCheckAI API
 * OpenAPI spec version: v1
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  InspectionDto,
  InspectionHistory
} from '../model';

import { customInstance } from '../../axios/axiosClient';





export const getShipinspectionsShipId = (
    shipId: string,
 signal?: AbortSignal
) => {
      
      
      return customInstance<InspectionHistory>(
      {url: `/shipinspections/${shipId}`, method: 'GET', signal
    },
      );
    }
  

export const getGetShipinspectionsShipIdQueryKey = (shipId: string,) => {
    return [`/shipinspections/${shipId}`] as const;
    }

    
export const getGetShipinspectionsShipIdQueryOptions = <TData = Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError = unknown>(shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError, TData>>, }
) => {

const {query: queryOptions} = options ?? {};

  const queryKey =  queryOptions?.queryKey ?? getGetShipinspectionsShipIdQueryKey(shipId);

  

    const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipinspectionsShipId>>> = ({ signal }) => getShipinspectionsShipId(shipId, signal);

      

      

   return  { queryKey, queryFn, enabled: !!(shipId), ...queryOptions} as UseQueryOptions<Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError, TData> & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type GetShipinspectionsShipIdQueryResult = NonNullable<Awaited<ReturnType<typeof getShipinspectionsShipId>>>
export type GetShipinspectionsShipIdQueryError = unknown


export function useGetShipinspectionsShipId<TData = Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError = unknown>(
 shipId: string, options: { query:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError, TData>> & Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getShipinspectionsShipId>>,
          TError,
          Awaited<ReturnType<typeof getShipinspectionsShipId>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetShipinspectionsShipId<TData = Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError = unknown>(
 shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError, TData>> & Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getShipinspectionsShipId>>,
          TError,
          Awaited<ReturnType<typeof getShipinspectionsShipId>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetShipinspectionsShipId<TData = Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError = unknown>(
 shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError, TData>>, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useGetShipinspectionsShipId<TData = Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError = unknown>(
 shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipinspectionsShipId>>, TError, TData>>, }
 , queryClient?: QueryClient 
 ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {

  const queryOptions = getGetShipinspectionsShipIdQueryOptions(shipId,options)

  const query = useQuery(queryOptions , queryClient) as  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey ;

  return query;
}



export const postShipinspectionsShipId = (
    shipId: string,
 signal?: AbortSignal
) => {
      
      
      return customInstance<InspectionDto>(
      {url: `/shipinspections/${shipId}`, method: 'POST', signal
    },
      );
    }
  


export const getPostShipinspectionsShipIdMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof postShipinspectionsShipId>>, TError,{shipId: string}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof postShipinspectionsShipId>>, TError,{shipId: string}, TContext> => {

const mutationKey = ['postShipinspectionsShipId'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof postShipinspectionsShipId>>, {shipId: string}> = (props) => {
          const {shipId} = props ?? {};

          return  postShipinspectionsShipId(shipId,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type PostShipinspectionsShipIdMutationResult = NonNullable<Awaited<ReturnType<typeof postShipinspectionsShipId>>>
    
    export type PostShipinspectionsShipIdMutationError = unknown

    export const usePostShipinspectionsShipId = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof postShipinspectionsShipId>>, TError,{shipId: string}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof postShipinspectionsShipId>>,
        TError,
        {shipId: string},
        TContext
      > => {

      const mutationOptions = getPostShipinspectionsShipIdMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    