/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * ShipCheckAI API
 * OpenAPI spec version: v1
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  InspectionDto,
  UpdateInspectionRequest
} from '../model';

import { customInstance } from '../../axios/axiosClient';





export const deleteInspectionsInspectionId = (
    inspectionId: string,
 ) => {
      
      
      return customInstance<void>(
      {url: `/inspections/${inspectionId}`, method: 'DELETE'
    },
      );
    }
  


export const getDeleteInspectionsInspectionIdMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof deleteInspectionsInspectionId>>, TError,{inspectionId: string}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof deleteInspectionsInspectionId>>, TError,{inspectionId: string}, TContext> => {

const mutationKey = ['deleteInspectionsInspectionId'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteInspectionsInspectionId>>, {inspectionId: string}> = (props) => {
          const {inspectionId} = props ?? {};

          return  deleteInspectionsInspectionId(inspectionId,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type DeleteInspectionsInspectionIdMutationResult = NonNullable<Awaited<ReturnType<typeof deleteInspectionsInspectionId>>>
    
    export type DeleteInspectionsInspectionIdMutationError = unknown

    export const useDeleteInspectionsInspectionId = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof deleteInspectionsInspectionId>>, TError,{inspectionId: string}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof deleteInspectionsInspectionId>>,
        TError,
        {inspectionId: string},
        TContext
      > => {

      const mutationOptions = getDeleteInspectionsInspectionIdMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    export const getInspectionsInspectionId = (
    inspectionId: string,
 signal?: AbortSignal
) => {
      
      
      return customInstance<InspectionDto>(
      {url: `/inspections/${inspectionId}`, method: 'GET', signal
    },
      );
    }
  

export const getGetInspectionsInspectionIdQueryKey = (inspectionId: string,) => {
    return [`/inspections/${inspectionId}`] as const;
    }

    
export const getGetInspectionsInspectionIdQueryOptions = <TData = Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError = unknown>(inspectionId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError, TData>>, }
) => {

const {query: queryOptions} = options ?? {};

  const queryKey =  queryOptions?.queryKey ?? getGetInspectionsInspectionIdQueryKey(inspectionId);

  

    const queryFn: QueryFunction<Awaited<ReturnType<typeof getInspectionsInspectionId>>> = ({ signal }) => getInspectionsInspectionId(inspectionId, signal);

      

      

   return  { queryKey, queryFn, enabled: !!(inspectionId), ...queryOptions} as UseQueryOptions<Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError, TData> & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type GetInspectionsInspectionIdQueryResult = NonNullable<Awaited<ReturnType<typeof getInspectionsInspectionId>>>
export type GetInspectionsInspectionIdQueryError = unknown


export function useGetInspectionsInspectionId<TData = Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError = unknown>(
 inspectionId: string, options: { query:Partial<UseQueryOptions<Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError, TData>> & Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInspectionsInspectionId>>,
          TError,
          Awaited<ReturnType<typeof getInspectionsInspectionId>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetInspectionsInspectionId<TData = Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError = unknown>(
 inspectionId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError, TData>> & Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInspectionsInspectionId>>,
          TError,
          Awaited<ReturnType<typeof getInspectionsInspectionId>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetInspectionsInspectionId<TData = Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError = unknown>(
 inspectionId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError, TData>>, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useGetInspectionsInspectionId<TData = Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError = unknown>(
 inspectionId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getInspectionsInspectionId>>, TError, TData>>, }
 , queryClient?: QueryClient 
 ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {

  const queryOptions = getGetInspectionsInspectionIdQueryOptions(inspectionId,options)

  const query = useQuery(queryOptions , queryClient) as  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey ;

  return query;
}



export const putInspectionsInspectionId = (
    inspectionId: string,
    updateInspectionRequest: UpdateInspectionRequest,
 ) => {
      
      
      return customInstance<InspectionDto>(
      {url: `/inspections/${inspectionId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: updateInspectionRequest
    },
      );
    }
  


export const getPutInspectionsInspectionIdMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof putInspectionsInspectionId>>, TError,{inspectionId: string;data: UpdateInspectionRequest}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof putInspectionsInspectionId>>, TError,{inspectionId: string;data: UpdateInspectionRequest}, TContext> => {

const mutationKey = ['putInspectionsInspectionId'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof putInspectionsInspectionId>>, {inspectionId: string;data: UpdateInspectionRequest}> = (props) => {
          const {inspectionId,data} = props ?? {};

          return  putInspectionsInspectionId(inspectionId,data,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type PutInspectionsInspectionIdMutationResult = NonNullable<Awaited<ReturnType<typeof putInspectionsInspectionId>>>
    export type PutInspectionsInspectionIdMutationBody = UpdateInspectionRequest
    export type PutInspectionsInspectionIdMutationError = unknown

    export const usePutInspectionsInspectionId = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof putInspectionsInspectionId>>, TError,{inspectionId: string;data: UpdateInspectionRequest}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof putInspectionsInspectionId>>,
        TError,
        {inspectionId: string;data: UpdateInspectionRequest},
        TContext
      > => {

      const mutationOptions = getPutInspectionsInspectionIdMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    