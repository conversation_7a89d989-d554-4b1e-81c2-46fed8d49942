/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * ShipCheckAI API
 * OpenAPI spec version: v1
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  CreateShipRequest,
  CreateShipResponse,
  DeleteShipResponse,
  GetShipResponse,
  GetUserShipsResponse,
  RemoveUserFromShipResponse,
  UpdateShipRequest,
  UpdateShipResponse
} from '../model';

import { customInstance } from '../../axios/axiosClient';





export const putShipsShipIdUsersUserToAdd = (
    shipId: string,
    userToAdd: string,
 ) => {
      
      
      return customInstance<void>(
      {url: `/ships/${shipId}/users/${userToAdd}`, method: 'PUT'
    },
      );
    }
  


export const getPutShipsShipIdUsersUserToAddMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof putShipsShipIdUsersUserToAdd>>, TError,{shipId: string;userToAdd: string}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof putShipsShipIdUsersUserToAdd>>, TError,{shipId: string;userToAdd: string}, TContext> => {

const mutationKey = ['putShipsShipIdUsersUserToAdd'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof putShipsShipIdUsersUserToAdd>>, {shipId: string;userToAdd: string}> = (props) => {
          const {shipId,userToAdd} = props ?? {};

          return  putShipsShipIdUsersUserToAdd(shipId,userToAdd,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type PutShipsShipIdUsersUserToAddMutationResult = NonNullable<Awaited<ReturnType<typeof putShipsShipIdUsersUserToAdd>>>
    
    export type PutShipsShipIdUsersUserToAddMutationError = unknown

    export const usePutShipsShipIdUsersUserToAdd = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof putShipsShipIdUsersUserToAdd>>, TError,{shipId: string;userToAdd: string}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof putShipsShipIdUsersUserToAdd>>,
        TError,
        {shipId: string;userToAdd: string},
        TContext
      > => {

      const mutationOptions = getPutShipsShipIdUsersUserToAddMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    export const postShips = (
    createShipRequest: CreateShipRequest,
 signal?: AbortSignal
) => {
      
      
      return customInstance<CreateShipResponse>(
      {url: `/ships`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: createShipRequest, signal
    },
      );
    }
  


export const getPostShipsMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof postShips>>, TError,{data: CreateShipRequest}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof postShips>>, TError,{data: CreateShipRequest}, TContext> => {

const mutationKey = ['postShips'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof postShips>>, {data: CreateShipRequest}> = (props) => {
          const {data} = props ?? {};

          return  postShips(data,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type PostShipsMutationResult = NonNullable<Awaited<ReturnType<typeof postShips>>>
    export type PostShipsMutationBody = CreateShipRequest
    export type PostShipsMutationError = unknown

    export const usePostShips = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof postShips>>, TError,{data: CreateShipRequest}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof postShips>>,
        TError,
        {data: CreateShipRequest},
        TContext
      > => {

      const mutationOptions = getPostShipsMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    export const getShips = (
    
 signal?: AbortSignal
) => {
      
      
      return customInstance<GetUserShipsResponse>(
      {url: `/ships`, method: 'GET', signal
    },
      );
    }
  

export const getGetShipsQueryKey = () => {
    return [`/ships`] as const;
    }

    
export const getGetShipsQueryOptions = <TData = Awaited<ReturnType<typeof getShips>>, TError = unknown>( options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShips>>, TError, TData>>, }
) => {

const {query: queryOptions} = options ?? {};

  const queryKey =  queryOptions?.queryKey ?? getGetShipsQueryKey();

  

    const queryFn: QueryFunction<Awaited<ReturnType<typeof getShips>>> = ({ signal }) => getShips(signal);

      

      

   return  { queryKey, queryFn, ...queryOptions} as UseQueryOptions<Awaited<ReturnType<typeof getShips>>, TError, TData> & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type GetShipsQueryResult = NonNullable<Awaited<ReturnType<typeof getShips>>>
export type GetShipsQueryError = unknown


export function useGetShips<TData = Awaited<ReturnType<typeof getShips>>, TError = unknown>(
  options: { query:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShips>>, TError, TData>> & Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getShips>>,
          TError,
          Awaited<ReturnType<typeof getShips>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetShips<TData = Awaited<ReturnType<typeof getShips>>, TError = unknown>(
  options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShips>>, TError, TData>> & Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getShips>>,
          TError,
          Awaited<ReturnType<typeof getShips>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetShips<TData = Awaited<ReturnType<typeof getShips>>, TError = unknown>(
  options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShips>>, TError, TData>>, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useGetShips<TData = Awaited<ReturnType<typeof getShips>>, TError = unknown>(
  options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShips>>, TError, TData>>, }
 , queryClient?: QueryClient 
 ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {

  const queryOptions = getGetShipsQueryOptions(options)

  const query = useQuery(queryOptions , queryClient) as  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey ;

  return query;
}



export const deleteShipsShipId = (
    shipId: string,
 ) => {
      
      
      return customInstance<DeleteShipResponse>(
      {url: `/ships/${shipId}`, method: 'DELETE'
    },
      );
    }
  


export const getDeleteShipsShipIdMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof deleteShipsShipId>>, TError,{shipId: string}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof deleteShipsShipId>>, TError,{shipId: string}, TContext> => {

const mutationKey = ['deleteShipsShipId'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteShipsShipId>>, {shipId: string}> = (props) => {
          const {shipId} = props ?? {};

          return  deleteShipsShipId(shipId,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type DeleteShipsShipIdMutationResult = NonNullable<Awaited<ReturnType<typeof deleteShipsShipId>>>
    
    export type DeleteShipsShipIdMutationError = unknown

    export const useDeleteShipsShipId = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof deleteShipsShipId>>, TError,{shipId: string}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof deleteShipsShipId>>,
        TError,
        {shipId: string},
        TContext
      > => {

      const mutationOptions = getDeleteShipsShipIdMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    export const getShipsShipId = (
    shipId: string,
 signal?: AbortSignal
) => {
      
      
      return customInstance<GetShipResponse>(
      {url: `/ships/${shipId}`, method: 'GET', signal
    },
      );
    }
  

export const getGetShipsShipIdQueryKey = (shipId: string,) => {
    return [`/ships/${shipId}`] as const;
    }

    
export const getGetShipsShipIdQueryOptions = <TData = Awaited<ReturnType<typeof getShipsShipId>>, TError = unknown>(shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipsShipId>>, TError, TData>>, }
) => {

const {query: queryOptions} = options ?? {};

  const queryKey =  queryOptions?.queryKey ?? getGetShipsShipIdQueryKey(shipId);

  

    const queryFn: QueryFunction<Awaited<ReturnType<typeof getShipsShipId>>> = ({ signal }) => getShipsShipId(shipId, signal);

      

      

   return  { queryKey, queryFn, enabled: !!(shipId), ...queryOptions} as UseQueryOptions<Awaited<ReturnType<typeof getShipsShipId>>, TError, TData> & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type GetShipsShipIdQueryResult = NonNullable<Awaited<ReturnType<typeof getShipsShipId>>>
export type GetShipsShipIdQueryError = unknown


export function useGetShipsShipId<TData = Awaited<ReturnType<typeof getShipsShipId>>, TError = unknown>(
 shipId: string, options: { query:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipsShipId>>, TError, TData>> & Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getShipsShipId>>,
          TError,
          Awaited<ReturnType<typeof getShipsShipId>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetShipsShipId<TData = Awaited<ReturnType<typeof getShipsShipId>>, TError = unknown>(
 shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipsShipId>>, TError, TData>> & Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getShipsShipId>>,
          TError,
          Awaited<ReturnType<typeof getShipsShipId>>
        > , 'initialData'
      >, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useGetShipsShipId<TData = Awaited<ReturnType<typeof getShipsShipId>>, TError = unknown>(
 shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipsShipId>>, TError, TData>>, }
 , queryClient?: QueryClient
  ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useGetShipsShipId<TData = Awaited<ReturnType<typeof getShipsShipId>>, TError = unknown>(
 shipId: string, options?: { query?:Partial<UseQueryOptions<Awaited<ReturnType<typeof getShipsShipId>>, TError, TData>>, }
 , queryClient?: QueryClient 
 ):  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {

  const queryOptions = getGetShipsShipIdQueryOptions(shipId,options)

  const query = useQuery(queryOptions , queryClient) as  UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey ;

  return query;
}



export const putShipsShipId = (
    shipId: string,
    updateShipRequest: UpdateShipRequest,
 ) => {
      
      
      return customInstance<UpdateShipResponse>(
      {url: `/ships/${shipId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: updateShipRequest
    },
      );
    }
  


export const getPutShipsShipIdMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof putShipsShipId>>, TError,{shipId: string;data: UpdateShipRequest}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof putShipsShipId>>, TError,{shipId: string;data: UpdateShipRequest}, TContext> => {

const mutationKey = ['putShipsShipId'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof putShipsShipId>>, {shipId: string;data: UpdateShipRequest}> = (props) => {
          const {shipId,data} = props ?? {};

          return  putShipsShipId(shipId,data,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type PutShipsShipIdMutationResult = NonNullable<Awaited<ReturnType<typeof putShipsShipId>>>
    export type PutShipsShipIdMutationBody = UpdateShipRequest
    export type PutShipsShipIdMutationError = unknown

    export const usePutShipsShipId = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof putShipsShipId>>, TError,{shipId: string;data: UpdateShipRequest}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof putShipsShipId>>,
        TError,
        {shipId: string;data: UpdateShipRequest},
        TContext
      > => {

      const mutationOptions = getPutShipsShipIdMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    export const deleteShipsShipIdUsersUserIdToRemove = (
    shipId: string,
    userIdToRemove: string,
 ) => {
      
      
      return customInstance<RemoveUserFromShipResponse>(
      {url: `/ships/${shipId}/users/${userIdToRemove}`, method: 'DELETE'
    },
      );
    }
  


export const getDeleteShipsShipIdUsersUserIdToRemoveMutationOptions = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof deleteShipsShipIdUsersUserIdToRemove>>, TError,{shipId: string;userIdToRemove: string}, TContext>, }
): UseMutationOptions<Awaited<ReturnType<typeof deleteShipsShipIdUsersUserIdToRemove>>, TError,{shipId: string;userIdToRemove: string}, TContext> => {

const mutationKey = ['deleteShipsShipIdUsersUserIdToRemove'];
const {mutation: mutationOptions} = options ?
      options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : {...options, mutation: {...options.mutation, mutationKey}}
      : {mutation: { mutationKey, }};

      


      const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteShipsShipIdUsersUserIdToRemove>>, {shipId: string;userIdToRemove: string}> = (props) => {
          const {shipId,userIdToRemove} = props ?? {};

          return  deleteShipsShipIdUsersUserIdToRemove(shipId,userIdToRemove,)
        }

        


  return  { mutationFn, ...mutationOptions }}

    export type DeleteShipsShipIdUsersUserIdToRemoveMutationResult = NonNullable<Awaited<ReturnType<typeof deleteShipsShipIdUsersUserIdToRemove>>>
    
    export type DeleteShipsShipIdUsersUserIdToRemoveMutationError = unknown

    export const useDeleteShipsShipIdUsersUserIdToRemove = <TError = unknown,
    TContext = unknown>(options?: { mutation?:UseMutationOptions<Awaited<ReturnType<typeof deleteShipsShipIdUsersUserIdToRemove>>, TError,{shipId: string;userIdToRemove: string}, TContext>, }
 , queryClient?: QueryClient): UseMutationResult<
        Awaited<ReturnType<typeof deleteShipsShipIdUsersUserIdToRemove>>,
        TError,
        {shipId: string;userIdToRemove: string},
        TContext
      > => {

      const mutationOptions = getDeleteShipsShipIdUsersUserIdToRemoveMutationOptions(options);

      return useMutation(mutationOptions , queryClient);
    }
    