import { createBrowserRouter } from 'react-router-dom';
import { ProtectedRoute } from '@/routes/ProtectedRoute';
import { AppLayout } from '@/components/layout/AppLayout';
import { LandingPage } from '@/pages/LandingPage';
import { DashboardPage } from '@/pages/DashboardPage';
import { ShipDetailsPage } from '@/pages/ShipDetailsPage';
import { ShipEditorPage } from '@/pages/ShipEditorPage';
import { InspectionPage } from '@/pages/InspectionPage';
import { InspectionHistoryPage } from '@/pages/InspectionHistoryPage';
import { InspectionReportPage } from '@/pages/InspectionReportPage';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <LandingPage />,
  },
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      {
        element: <AppLayout />,
        children: [
          {
            path: 'dashboard',
            element: <DashboardPage />,
          },
          {
            path: 'ship/new',
            element: <ShipEditorPage />,
          },
          {
            path: 'ship/:shipId',
            element: <ShipDetailsPage />,
          },
          {
            path: 'ship/:shipId/edit',
            element: <ShipEditorPage />,
          },
          {
            path: 'ship/:shipId/inspections',
            element: <InspectionHistoryPage />,
          },
          {
            path: 'inspection/:inspectionId',
            element: <InspectionPage />,
          },
          {
            path: 'inspection/:inspectionId/report',
            element: <InspectionReportPage />,
          },
        ],
      },
    ],
  },
]);