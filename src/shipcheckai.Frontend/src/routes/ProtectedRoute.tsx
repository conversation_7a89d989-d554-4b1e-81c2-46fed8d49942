import { MsalAuthenticationTemplate } from '@azure/msal-react';
import { InteractionType } from '@azure/msal-browser';
import { Outlet } from 'react-router-dom';
import { Alert, Box, CircularProgress } from '@mui/material';
import { loginRequest } from '@/lib/msal';

type ProtectedRouteProps = {};
export function ProtectedRoute(_: ProtectedRouteProps) {
  const Loading = () => (
    <Box sx={{ display: 'grid', placeItems: 'center', py: 8 }}>
      <CircularProgress />
    </Box>
  );
  const ErrorView = () => <Alert severity="error">Authentication error</Alert>;

  return (
    <MsalAuthenticationTemplate
      interactionType={InteractionType.Redirect}
      authenticationRequest={loginRequest}
      loadingComponent={Loading}
      errorComponent={ErrorView}
    >
      <Outlet />
    </MsalAuthenticationTemplate>
  );
}

