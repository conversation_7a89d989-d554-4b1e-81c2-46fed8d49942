import { Ms<PERSON><PERSON><PERSON><PERSON> } from '@azure/msal-react';
import { msalInstance } from '@/lib/msal';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { theme } from '@/lib/theme';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { queryClient } from '@/lib/queryClient';
import { RouterProvider } from 'react-router-dom';
import { router } from '@/routes/AppRouter';

type AppProvidersProps = { children?: React.ReactNode };
export function AppProviders(_: AppProvidersProps) {
  return (
    <MsalProvider instance={msalInstance}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <RouterProvider router={router} />
          <ReactQueryDevtools initialIsOpen={false} />
        </ThemeProvider>
      </QueryClientProvider>
    </MsalProvider>
  );
}

