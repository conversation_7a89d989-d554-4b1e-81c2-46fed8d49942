import { useGetShipsShipId, usePostShips, usePutShipsShipId } from '@/api/endpoints/ships';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Button, CircularProgress, TextField, Typography, Paper } from '@mui/material';
import { useEffect, useState } from 'react';
import type {CreateShipRequest, UpdateShipRequest} from '@/api/model';

interface ShipFormData {
  name: string;
  imoNumber: string;
  description?: string;
}

interface FormErrors {
  name?: string;
  imoNumber?: string;
}

export function ShipEditorPage() {
  const { shipId } = useParams<{ shipId: string }>();
  const navigate = useNavigate();
  const isEditMode = Boolean(shipId);

  const { data: ship, isLoading: isLoadingShip } = useGetShipsShipId(shipId!, {
    query: { enabled: isEditMode },
  });

  const createShip = usePostShips();
  const updateShip = usePutShipsShipId();

  const [formData, setFormData] = useState<ShipFormData>({ name: '', imoNumber: '', description: '' });
  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    if (isEditMode && ship) {
      setFormData({
        name: ship.name || '',
        imoNumber: ship.imoNumber || '',
        description: ship.description || '',
      });
    }
  }, [isEditMode, ship]);

  const validate = (): boolean => {
    const newErrors: FormErrors = {};
    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.imoNumber) newErrors.imoNumber = 'IMO number is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!validate()) return;

    if (isEditMode && shipId) {
      await updateShip.mutateAsync({ shipId, data: formData as UpdateShipRequest });
      navigate(`/ship/${shipId}`);
    } else {
      const newShip = await createShip.mutateAsync({ data: formData as CreateShipRequest });
      navigate(`/ship/${newShip.data.id}`);
    }
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  if (isLoadingShip) {
    return <CircularProgress />;
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {isEditMode ? 'Edit Ship' : 'Create New Ship'}
      </Typography>
      <Box component="form" onSubmit={handleSubmit} noValidate>
        <TextField
          name="name"
          label="Ship Name"
          variant="outlined"
          fullWidth
          margin="normal"
          value={formData.name}
          onChange={handleChange}
          error={!!errors.name}
          helperText={errors.name}
        />
        <TextField
          name="imoNumber"
          label="IMO Number"
          variant="outlined"
          fullWidth
          margin="normal"
          value={formData.imoNumber}
          onChange={handleChange}
          error={!!errors.imoNumber}
          helperText={errors.imoNumber}
        />
        <TextField
          name="description"
          label="Description"
          variant="outlined"
          fullWidth
          margin="normal"
          multiline
          rows={4}
          value={formData.description}
          onChange={handleChange}
        />
        {/* TODO: Photo upload component */}
        <Box sx={{ mt: 2 }}>
          <Button type="submit" variant="contained" disabled={createShip.isPending || updateShip.isPending}>
            {isEditMode ? 'Save Changes' : 'Create Ship'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
}