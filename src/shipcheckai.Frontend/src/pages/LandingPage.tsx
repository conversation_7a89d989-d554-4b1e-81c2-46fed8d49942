
import { useMsal } from '@azure/msal-react';
import { loginRequest } from '@/lib/msal';
import { Box, Button, Container, Typography } from '@mui/material';

export function LandingPage() {
  const { instance } = useMsal();

  const handleLogin = () => {
    instance.loginRedirect(loginRequest).catch((e) => {
      console.error(e);
    });
  };

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          py: 4,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <Typography variant="h1" component="h1" gutterBottom>
          ShipCheck.AI
        </Typography>
        <Typography variant="h5" component="h2" gutterBottom align="center">
          A seamless and efficient way for ship inspectors and owners to conduct and manage ship inspections, ensuring safety and compliance with industry standards.
        </Typography>
        <Box sx={{ pt: 4 }}>
          <Button variant="contained" size="large" onClick={handleLogin}>
            Login / Register
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
