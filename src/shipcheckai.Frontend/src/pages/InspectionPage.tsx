
import { useGetInspectionsInspectionId, usePutInspectionsInspectionId, useDeleteInspectionsInspectionId } from '@/api/endpoints/inspections';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Button, CircularProgress, Typography, Paper, Grid, Card, CardContent, TextField, CardMedia } from '@mui/material';
import { useEffect, useState } from 'react';
import type { ChecklistEntryDto, UpdateInspectionRequest } from '@/api/model';

export function InspectionPage() {
  const { inspectionId } = useParams<{ inspectionId: string }>();
  const navigate = useNavigate();

  const { data: inspection, isLoading, error } = useGetInspectionsInspectionId(inspectionId!);
  const updateInspection = usePutInspectionsInspectionId();
  const deleteInspection = useDeleteInspectionsInspectionId();

  const [checklist, setChecklist] = useState<ChecklistEntryDto[]>([]);

  useEffect(() => {
    if (inspection?.checklist) {
      setChecklist(inspection.checklist);
    }
  }, [inspection]);

  const handleCommentChange = (index: number, value: string) => {
    const newChecklist = [...checklist];
    newChecklist[index].comments = [value]; // Assuming one comment per entry for now
    setChecklist(newChecklist);
  };

  const handleImageUpload = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const newChecklist = [...checklist];
        if (!newChecklist[index].images) {
          newChecklist[index].images = [];
        }
        newChecklist[index].images?.push(e.target?.result as string);
        setChecklist(newChecklist);
      };
      reader.readAsDataURL(event.target.files[0]);
    }
  };

  const handleSave = async () => {
    if (!inspectionId) return;
    const updatedInspection: UpdateInspectionRequest = { entries: checklist };
    await updateInspection.mutateAsync({ inspectionId, data: updatedInspection });
    navigate('/dashboard');
  };

  const handleDelete = async () => {
    if (!inspectionId) return;
    await deleteInspection.mutateAsync({ inspectionId });
    navigate('/dashboard');
  };

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Typography color="error">Error loading inspection.</Typography>;
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Inspection
      </Typography>
      {checklist.map((entry, index) => (
        <Card key={index} sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6">{entry.positionName}</Typography>
            <TextField
              label="Comments"
              variant="outlined"
              fullWidth
              margin="normal"
              multiline
              rows={2}
              value={entry.comments?.[0] || ''}
              onChange={(e) => handleCommentChange(index, e.target.value)}
            />
            <Grid container spacing={2} sx={{ mt: 1 }}>
              {entry.images?.map((image, i) => (
                <Grid item xs={12} sm={6} md={4} key={i} component="div">
                  <CardMedia component="img" image={image} sx={{ height: 140 }} />
                </Grid>
              ))}
            </Grid>
            <Button variant="contained" component="label" sx={{ mt: 1 }}>
              Upload Photo
              <input type="file" hidden accept="image/*" onChange={(e) => handleImageUpload(index, e)} />
            </Button>
          </CardContent>
        </Card>
      ))}
      <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
        <Button variant="contained" onClick={handleSave}>Save and Exit</Button>
        <Button variant="contained" color="primary" onClick={handleSave}>Submit Inspection</Button>
        <Button variant="outlined" color="error" onClick={handleDelete}>Delete Draft</Button>
      </Box>
    </Paper>
  );
}
