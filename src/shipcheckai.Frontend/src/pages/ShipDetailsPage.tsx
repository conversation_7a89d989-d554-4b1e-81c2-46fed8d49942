
import { useGetShipsShipId, useDeleteShipsShipId } from '@/api/endpoints/ships';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Button, CircularProgress, Typography, Paper, Grid, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import { useMsal } from '@azure/msal-react';
import { useState } from 'react';

export function ShipDetailsPage() {
  const { shipId } = useParams<{ shipId: string }>();
  const navigate = useNavigate();
  const { accounts } = useMsal();
  const { data: ship, isLoading, error } = useGetShipsShipId(shipId!);
  const deleteShip = useDeleteShipsShipId();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const userId = accounts[0]?.idTokenClaims?.oid;
  const isOwner = ship?.creatorId === userId;

  const handleDelete = async () => {
    if (!shipId) return;
    await deleteShip.mutateAsync({ shipId });
    navigate('/dashboard');
  };

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Typography color="error">Error loading ship details.</Typography>;
  }

  if (!ship) {
    return <Typography>Ship not found.</Typography>;
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {ship.name}
        {isOwner && <Typography component="span" color="primary"> (Owner)</Typography>}
      </Typography>
      <Typography variant="h6" gutterBottom>
        IMO: {ship.imoNumber}
      </Typography>
      <Typography variant="body1" gutterBottom>
        {ship.description}
      </Typography>

      <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
        {isOwner && (
          <>
            <Button variant="contained" onClick={() => navigate(`/ship/${shipId}/edit`)}>Edit Ship</Button>
            <Button variant="contained" color="error" onClick={() => setOpenDeleteDialog(true)}>Delete Ship</Button>
            <Button variant="outlined">Manage Users</Button> {/* Placeholder */}
          </>
        )}
        <Button variant="contained" color="secondary" onClick={() => navigate(`/ship/${shipId}/inspections`)}>View Inspection History</Button>
        <Button variant="contained" color="secondary">Start New Inspection</Button> {/* Placeholder */}
      </Box>

      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Delete Ship?</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this ship? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
          <Button onClick={handleDelete} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
}
