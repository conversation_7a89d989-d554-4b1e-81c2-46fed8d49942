
import { useGetShipinspectionsShipId } from '@/api/endpoints/shipinspections';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, CircularProgress, Typography, Paper, List, ListItem, ListItemText, Divider } from '@mui/material';

export function InspectionHistoryPage() {
  const { shipId } = useParams<{ shipId: string }>();
  const navigate = useNavigate();
  const { data: inspectionHistory, isLoading, error } = useGetShipinspectionsShipId(shipId!);

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Typography color="error">Error loading inspection history.</Typography>;
  }

  if (!inspectionHistory || inspectionHistory.length === 0) {
    return <Typography>No inspection history found for this ship.</Typography>;
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Inspection History
      </Typography>
      <List>
        {inspectionHistory?.data.map((inspection, index) => (
          <div key={inspection.id}>
            <ListItem disablePadding>
              <ListItemButton onClick={() => navigate(`/inspection/${inspection.id}/report`)}>
                <ListItemText
                  primary={`Inspection Date: ${new Date(inspection.date).toLocaleDateString()}`}
                  secondary={`Inspector: ${inspection.inspectorName}`}
                />
              </ListItemButton>
            </ListItem>
            {index < inspectionHistory?.maintenanceHistory?.length - 1 && <Divider />}
          </div>
        ))}
      </List>
    </Paper>
  );
}
