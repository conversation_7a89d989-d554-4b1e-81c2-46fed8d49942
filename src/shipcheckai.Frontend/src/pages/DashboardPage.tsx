
import { useGetShips } from '@/api/endpoints/ships';
import { Box, Button, Card, CardActionArea, CardContent, CircularProgress, Grid, Typography } from '@mui/material';
import { useMsal } from '@azure/msal-react';
import { useNavigate } from 'react-router-dom';

export function DashboardPage() {
  const { accounts } = useMsal();
  const navigate = useNavigate();
  const { data: ships, isLoading, error } = useGetShips();

  const userName = accounts[0]?.name;

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Typography color="error">Error loading ships.</Typography>;
  }

  // TODO: Filter in-progress inspections
  const inProgressInspections = [];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Welcome, {userName || 'User'}
      </Typography>

      <Box sx={{ my: 4 }}>
        <Typography variant="h5" gutterBottom>
          My In-Progress Inspections
        </Typography>
        {inProgressInspections.length === 0 ? (
          <Typography>No in-progress inspections.</Typography>
        ) : (
          <Grid container spacing={2}>
            {/* Render in-progress inspections here */}
          </Grid>
        )}
      </Box>

      <Box sx={{ my: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">
            My Ships
          </Typography>
          <Button variant="contained" onClick={() => navigate('/ship/new')}>
            Create New Ship
          </Button>
        </Box>
        <Grid container spacing={2}>
          {ships?.ships?.map((ship) => (
            <Grid item xs={12} sm={6} md={4} key={ship.id} component="div">
              <Card onClick={() => navigate(`/ship/${ship.id}`)}>
                <CardActionArea>
                  <CardContent>
                    <Typography gutterBottom variant="h6" component="div">
                      {ship.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      IMO: {ship.imoNumber}
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
}
