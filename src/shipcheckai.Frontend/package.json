{"name": "shipcheckai-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "orval": "orval"}, "dependencies": {"@azure/msal-browser": "^4.18.0", "@azure/msal-react": "^3.0.16", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/roboto": "^5.2.6", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@tanstack/react-query": "^5.81.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "vite-tsconfig-paths": "^5.1.4", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.29.0", "@openapitools/openapi-generator-cli": "^2.21.0", "@tanstack/react-query-devtools": "^5.84.1", "@types/node": "^22.12.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "openapi-typescript": "^7.8.0", "orval": "^7.10.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}