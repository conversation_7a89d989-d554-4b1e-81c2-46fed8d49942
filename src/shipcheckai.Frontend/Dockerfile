# Stage 1: Build the frontend with Node
FROM node:20 as build

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm install

COPY . .
RUN npm run build

# Stage 2: Serve with Nginx, using envsubst for config templating
FROM nginx:alpine

# Copy built frontend files
COPY --from=build /app/dist /usr/share/nginx/html

# Copy the Nginx template
COPY --from=build /app/default.conf.template /etc/nginx/templates/default.conf.template

# Copy entrypoint script that performs envsubst
COPY --from=build /app/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Entrypoint to substitute env vars and start nginx
ENTRYPOINT ["/entrypoint.sh"]

# Default Nginx port
EXPOSE 80