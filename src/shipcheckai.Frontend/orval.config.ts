export default {
  api: {
    input: {
      target: '../shipcheckai.Api/openapi.json',
    },
    output: {
      mode: 'tags',
      target: 'src/api/endpoints',
      schemas: 'src/api/model',
      client: 'react-query',
      mock: false,
      // baseUrl: '/api',
      override: {
        mutator: {
          path: './src/axios/axiosClient.ts',
          name: 'customInstance',
        },
      },
    },
  },
};