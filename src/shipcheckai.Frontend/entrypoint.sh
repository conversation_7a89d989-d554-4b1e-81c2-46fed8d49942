#!/bin/sh
# Create conf.d folder if not exists
mkdir -p /etc/nginx/conf.d

# Substitute only specific env vars into default.conf.template
# This prevents nginx variables like $proxy_add_x_forwarded_for from being substituted
envsubst '${FRONTEND_PORT} ${services__apiservice__https__0}' < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf

# Optional: Show the final config for debugging
echo "======= Final Nginx Config ======="
cat /etc/nginx/conf.d/default.conf
echo "=================================="

# Start nginx
exec nginx -g 'daemon off;'