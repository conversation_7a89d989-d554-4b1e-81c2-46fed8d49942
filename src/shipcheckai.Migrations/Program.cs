using Microsoft.EntityFrameworkCore;
using shipcheckai.Entities.Data;
using shipcheckai.Migrations;

var builder = Host.CreateApplicationBuilder(args);

builder.Services.AddDbContext<ShipDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("ships"),
        optionsBuilder => { optionsBuilder.ConfigureDataSource(x => x.EnableDynamicJson()); }));

builder.Services.AddHostedService<Worker>();

var host = builder.Build();
host.Run();