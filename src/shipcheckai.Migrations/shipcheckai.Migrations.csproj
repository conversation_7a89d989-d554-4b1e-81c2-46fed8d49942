<Project Sdk="Microsoft.NET.Sdk.Worker">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>dotnet-shipcheckai.Migrations-12fedb15-ffe7-4fe6-af59-3d7848c97589</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.8" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\shipcheckai.Entities\shipcheckai.Entities.csproj" />
      <ProjectReference Include="..\shipcheckai.ServiceDefaults\shipcheckai.ServiceDefaults.csproj" />
    </ItemGroup>
</Project>
