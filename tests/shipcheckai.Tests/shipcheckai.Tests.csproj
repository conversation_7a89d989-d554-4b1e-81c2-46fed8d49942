<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.2"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0"/>
        <PackageReference Include="Shouldly" Version="4.3.0" />
        <PackageReference Include="xunit" Version="2.9.2"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2"/>
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.8" />
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="Testcontainers.PostgreSql" Version="4.1.0" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\shipcheckai.Api\shipcheckai.Api.csproj" />
        <ProjectReference Include="..\..\src\shipcheckai.Entities\shipcheckai.Entities.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Services\" />
      <Folder Include="TestHelpers\" />
    </ItemGroup>

</Project>
